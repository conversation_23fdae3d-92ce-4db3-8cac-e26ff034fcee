# 转发节点详细设计

## 目录

1. [系统概述](#1-系统概述)
2. [系统架构](#2-系统架构)
3. [技术选型](#3-技术选型)
4. [功能设计](#4-功能设计)
5. [内部模块架构](#5-内部模块架构)
6. [核心数据结构](#6-核心数据结构)
7. [状态管理机制](#7-状态管理机制)
8. [错误处理机制](#8-错误处理机制)
9. [性能优化策略](#9-性能优化策略)
10. [安全机制](#10-安全机制)
11. [配置管理](#11-配置管理)
12. [监控和日志](#12-监控和日志)
13. [API设计](#13-api设计)
14. [数据存储设计](#14-数据存储设计)
15. [部署架构](#15-部署架构)
16. [测试策略](#16-测试策略)
17. [运维监控](#17-运维监控)
18. [性能调优](#18-性能调优)
19. [开发指南](#19-开发指南)
20. [故障排查指南](#20-故障排查指南)

---

## 1. 系统概述

### 1.1 系统定位

转发节点（Forwarder Node）是KVM隧道服务的核心组件，作为分布式媒体转发网络的基础节点，负责处理KVM设备间的音视频流转发、控制信令传输和跨节点通信。

### 1.2 核心价值

- **高性能转发**：基于MediaSoup引擎，提供低延迟、高质量的音视频流转发
- **分布式架构**：支持多节点部署，实现负载均衡和就近接入
- **安全可靠**：采用mTLS双向认证，确保节点间通信安全
- **弹性扩展**：支持动态扩容，适应不同规模的部署需求

### 1.3 系统边界

```mermaid
graph TB
    subgraph "外部系统"
        MS[转发管理服务器]
        KMS[KVM管理服务器]
        TX[KVM TX设备]
        RX[KVM RX设备]
    end

    subgraph "转发节点系统边界"
        FN[转发节点]
    end

    subgraph "其他转发节点"
        FN2[转发节点2]
        FN3[转发节点3]
    end

    MS -->|管理调用| FN
    FN -->|心跳上报| MS
    TX -->|媒体流/控制| FN
    FN -->|媒体流/控制| RX
    FN <-->|节点间通信| FN2
    FN <-->|节点间通信| FN3

    style FN fill:#e1f5fe
    style MS fill:#fff3e0
    style TX fill:#e8f5e8
    style RX fill:#e8f5e8
```

### 1.4 关键特性

- **多协议支持**：支持WebRTC、TCP/UDP隧道、组播等多种传输协议
- **智能路由**：基于网络拓扑和负载情况，自动选择最优转发路径
- **故障自愈**：具备故障检测和自动恢复能力
- **监控可观测**：提供丰富的监控指标和日志，支持运维管理

## 2. 系统架构

### 2.1 整体架构

```mermaid
graph TB
    subgraph "管理层"
        MS[转发管理服务器]
        KMS[KVM管理服务器]
    end

    subgraph "转发层"
        subgraph "区域A"
            FN1[转发节点1]
            FN2[转发节点2]
        end

        subgraph "区域B"
            FN3[转发节点3]
            FN4[转发节点4]
        end
    end

    subgraph "设备层"
        subgraph "区域A设备"
            TX1[KVM TX 1]
            RX1[KVM RX 1]
        end

        subgraph "区域B设备"
            TX2[KVM TX 2]
            RX2[KVM RX 2]
        end
    end

    MS --> FN1
    MS --> FN2
    MS --> FN3
    MS --> FN4

    FN1 <--> FN3
    FN2 <--> FN4
    FN1 <--> FN2
    FN3 <--> FN4

    TX1 --> FN1
    FN1 --> RX1
    TX2 --> FN3
    FN3 --> RX2

    FN1 -.->|跨区域转发| FN3

    style MS fill:#ff9800
    style FN1 fill:#2196f3
    style FN2 fill:#2196f3
    style FN3 fill:#2196f3
    style FN4 fill:#2196f3
```

### 2.2 分层架构

转发节点采用分层架构设计，从下到上分为：

1. **基础设施层**：网络、存储、计算资源
2. **传输层**：MediaSoup、TCP/UDP隧道、组播控制
3. **业务层**：会话管理、节点管理、传输管理
4. **接口层**：REST API、WebSocket、节点间通信
5. **管理层**：配置、监控、日志、安全

## 3. 技术选型

### 3.1 核心技术栈

| 技术领域 | 选型 | 版本 | 选型理由 |
|---------|------|------|----------|
| 编程语言 | Rust | 1.70+ | 内存安全、高性能、并发友好 |
| 异步运行时 | Tokio | 1.0+ | 成熟的异步运行时，生态丰富 |
| Web框架 | Axum | 0.7+ | 基于Tokio，性能优异，类型安全 |
| 媒体处理 | MediaSoup | 3.12+ | 专业的WebRTC媒体服务器 |
| 序列化 | Serde | 1.0+ | Rust生态标准序列化库 |
| 配置管理 | Config | 0.13+ | 支持多种配置格式 |
| 日志系统 | Tracing | 0.1+ | 结构化日志，性能优异 |
| 监控指标 | Prometheus | - | 业界标准监控系统 |
| 容器化 | Docker | 20.10+ | 标准化部署方案 |
| 编排工具 | Kubernetes | 1.25+ | 容器编排和管理 |

### 3.2 依赖管理策略

#### 3.2.1 版本管理原则
- **主要依赖**：锁定大版本，跟踪小版本更新
- **安全补丁**：及时更新安全相关依赖
- **兼容性测试**：每次依赖更新都进行完整测试
- **回滚机制**：保持依赖更新的可回滚性

#### 3.2.2 依赖分类
```toml
[dependencies]
# 核心运行时依赖 - 严格版本控制
tokio = { version = "=1.32.0", features = ["full"] }
axum = { version = "=0.7.2", features = ["ws"] }

# 功能性依赖 - 兼容性版本控制
serde = { version = "^1.0.188", features = ["derive"] }
tracing = "^0.1.37"

# 开发依赖 - 宽松版本控制
[dev-dependencies]
criterion = "0.5"
tokio-test = "0.4"
```

## 4. 功能设计

转发节点是KVM隧道服务的核心组件，负责处理所有数据转发和路由功能。

### 4.1 核心功能

*   **数据转发**：
    *   创建 mediasoup WebRTCTransport 与客户端交互。
    *   创建 mediasoup PipeTransport 与其他转发节点交互。
    *   创建 mediasoup PlainTransport 来接收KVM TX设备流媒体组播。
    *   创建 mediasoup PlainTransport 并转发成组播供KVM RX设备使用。
    *   创建KVM TX设备与KVM RX设备连接的TCP/UDP隧道。
    *   管理 Producer 和 Consumer，实现数据流从源到目的的转发。
*   **节点间通信 (Inter-Node Communication)**：
    *   当KVM RX设备请求的KVM TX设备位于另一节点时，发起节点向目标节点请求建立数据通路。
    *   使用 mediasoup 的 `pipeToRouter` 功能在不同节点的 Router 之间高效转发媒体流。
    *   同步必要的控制信令。
*   **与转发管理服务器通信**：
    *   获取转发管理服务器区域列表，向转发管理服务器注册。
    *   定期发送心跳以维持在线状态。
    *   接收转发管理服务器的控制请求。

### 4.2 核心流程

#### 4.2.1 转发节点启动和注册流程
```mermaid
sequenceDiagram
    participant FN as 转发节点
    participant MS as 转发管理服务器
    participant KMS as KVM管理服务器(本地)

    Note over FN: 节点启动
    FN->>FN: 初始化 mediasoup Worker
    FN->>FN: 创建主 Router
    FN->>FN: 配置网络端口和安全策略
    
    FN->>+MS: 注册节点 (NodeInfo)
    MS->>MS: 验证节点信息
    MS->>MS: 存储节点信息，标记为在线
    MS-->>-FN: 注册成功
    
    loop 心跳维持
        FN->>MS: 心跳包 (负载信息、连接数)
        MS->>MS: 更新节点状态和负载
        MS-->>FN: 心跳响应 (配置更新)
    end
```

#### 4.2.2 跨节点访问请求处理流程
```mermaid
sequenceDiagram
    participant C as KVM RX设备
    participant KMS as KVM管理服务器
    participant FN_Client as 转发节点 (KVM RX接入)
    participant FN_Target as 转发节点 (KVM TX接入)

    participant MS as 转发管理服务器
    participant T as KVM TX设备

    C->>+KMS: 请求访问 TargetID
    KMS->>+MS: 请求转发
    MS->>MS: 查找设备所在节点
    MS->>+FN_Target: 请求转发
    FN_Target->>FN_Target: 检查目标设备状态
    FN_Target-->>-MS: 返回转发会话ID
    MS->>+FN_Client: 请求转发
    FN_Client->>+FN_Target: 请求跨节点访问
    FN_Target->>FN_Target: 验证请求合法性
    FN_Target->>FN_Target: 创建 PipeTransport，TCP/UDP隧道(目标侧)
    FN_Target-->>FN_Client: 返回 PipeTransport、TCP/UDP隧道参数
    FN_Client->>FN_Client: 创建 PipeTransport，TCP/UDP隧道(客户端侧)
    Note over FN_Client,FN_Target: 建立节点间PipeTransport，TCP/UDP隧道
    
    Note over FN_Client,FN_Target: 配置媒体流管道
    FN_Target->>FN_Target: 准备 Producer/Consumer
    FN_Client->>FN_Client: 准备 Producer/Consumer
    
    FN_Client-->>-MS: 建立连接成功，返回虚拟IP与会话ID
    MS -->>-KMS: 转发成功，返回虚拟IP
    KMS -->>-C: 返回虚拟IP
    
    Note over C,T: 建立端到端数据通路
    rect rgb(230, 255, 230)
        Note over C,FN_Client: 组播，TCP/UDP
        C->>FN_Client: 媒体流/控制数据
        Note over FN_Client,FN_Target: Pipe Transport，TCP/UDP隧道
        FN_Client->>FN_Target: 转发数据
        Note over FN_Target,T: 组播，TCP/UDP
        FN_Target->>T: 转发到设备
    end
```

## 5. 内部模块架构

转发节点采用模块化设计，主要包含以下核心模块：

### 5.1 架构图
```mermaid
graph TB
    subgraph "转发节点 (Forwarder Node)"
        subgraph "核心层 (Core Layer)"
            NM[节点管理器<br/>NodeManager]
            SM[会话管理器<br/>SessionManager]
            TM[传输管理器<br/>TransportManager]
            CM[连接管理器<br/>ConnectionManager]
        end
        
        subgraph "传输层 (Transport Layer)"
            MS[MediaSoup引擎<br/>MediaSoupEngine]
            PT[端口转发器<br/>PortForwarder]
            MC[组播控制器<br/>MulticastController]
        end
        
        subgraph "网络层 (Network Layer)"
            API[API服务器<br/>APIServer]
            WS[WebSocket服务器<br/>WebSocketServer]
            TCP[TCP隧道<br/>TCPTunnel]
            UDP[UDP隧道<br/>UDPTunnel]
        end
        
        subgraph "管理层 (Management Layer)"
            CFG[配置管理器<br/>ConfigManager]
            LOG[日志管理器<br/>LogManager]
            MON[监控管理器<br/>MonitorManager]
            SEC[安全管理器<br/>SecurityManager]
        end
        
        subgraph "存储层 (Storage Layer)"
            MEM[内存存储<br/>MemoryStore]
            CACHE[缓存管理<br/>CacheManager]
        end
    end
    
    NM --> SM
    SM --> TM
    TM --> CM
    TM --> MS
    TM --> PT
    TM --> MC
    CM --> TCP
    CM --> UDP
    API --> NM
    WS --> SM
    CFG --> NM
    LOG --> MON
    SEC --> API
    MEM --> SM
    CACHE --> TM
    
    style NM fill:#e1f5fe
    style SM fill:#e1f5fe
    style TM fill:#e1f5fe
    style CM fill:#e1f5fe
    style MS fill:#e8f5e8
    style PT fill:#e8f5e8
    style MC fill:#e8f5e8
    style API fill:#fff3e0
    style WS fill:#fff3e0
    style TCP fill:#fff3e0
    style UDP fill:#fff3e0
    style CFG fill:#fce4ec
    style LOG fill:#fce4ec
    style MON fill:#fce4ec
    style SEC fill:#fce4ec
    style MEM fill:#f3e5f5
    style CACHE fill:#f3e5f5
```

### 5.2 核心模块详细设计

#### 5.2.1 节点管理器 (NodeManager)
**职责**：
- 管理节点的生命周期（启动、运行、关闭）
- 与转发管理服务器的通信和注册
- 心跳维持和状态同步
- 全局配置的接收和应用

**主要接口**：
```rust
pub trait NodeManager {
    async fn start(&self) -> Result<(), NodeError>;
    async fn stop(&self) -> Result<(), NodeError>;
    async fn register_to_management_server(&self) -> Result<(), NodeError>;
    async fn send_heartbeat(&self) -> Result<(), NodeError>;
    async fn update_config(&self, config: NodeConfig) -> Result<(), NodeError>;
    fn get_node_info(&self) -> NodeInfo;
    fn get_node_status(&self) -> NodeStatus;
}
```

#### 5.2.2 会话管理器 (SessionManager)
**职责**：
- 管理所有活跃的转发会话
- 处理会话的创建、更新、销毁
- 维护会话状态和元数据
- 协调不同类型的传输会话

**主要接口**：
```rust
pub trait SessionManager {
    async fn create_transmitter_session(&self, config: TransmitterConfig) -> Result<SessionId, SessionError>;
    async fn create_receiver_session(&self, config: ReceiverConfig) -> Result<SessionId, SessionError>;
    async fn destroy_session(&self, session_id: SessionId) -> Result<(), SessionError>;
    async fn get_session_info(&self, session_id: SessionId) -> Result<SessionInfo, SessionError>;
    fn list_sessions(&self) -> Vec<SessionSummary>;
    async fn update_session_status(&self, session_id: SessionId, status: SessionStatus) -> Result<(), SessionError>;
}
```

#### 5.2.3 传输管理器 (TransportManager)
**职责**：
- 协调各种传输协议的使用
- 管理MediaSoup、端口转发、组播等传输方式
- 处理跨节点的传输连接
- 优化传输路径和性能

**主要接口**：
```rust
pub trait TransportManager {
    async fn create_mediasoup_transport(&self, config: MediaSoupConfig) -> Result<TransportId, TransportError>;
    async fn create_port_forward(&self, config: PortForwardConfig) -> Result<TransportId, TransportError>;
    async fn create_multicast_transport(&self, config: MulticastConfig) -> Result<TransportId, TransportError>;
    async fn establish_inter_node_pipe(&self, target_node: NodeId, config: PipeConfig) -> Result<PipeId, TransportError>;
    async fn destroy_transport(&self, transport_id: TransportId) -> Result<(), TransportError>;
    fn get_transport_stats(&self, transport_id: TransportId) -> Option<TransportStats>;
}
```

#### 5.2.4 连接管理器 (ConnectionManager)
**职责**：
- 管理与KVM设备的直接连接
- 处理TCP/UDP隧道的建立和维护
- 连接池的管理和优化
- 连接状态监控和异常处理

**主要接口**：
```rust
pub trait ConnectionManager {
    async fn create_tcp_tunnel(&self, config: TcpTunnelConfig) -> Result<TunnelId, ConnectionError>;
    async fn create_udp_tunnel(&self, config: UdpTunnelConfig) -> Result<TunnelId, ConnectionError>;
    async fn close_tunnel(&self, tunnel_id: TunnelId) -> Result<(), ConnectionError>;
    fn get_tunnel_status(&self, tunnel_id: TunnelId) -> Option<TunnelStatus>;
    async fn health_check(&self) -> HealthStatus;
}
```

## 6. 核心数据结构

### 6.1 节点相关数据结构
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeInfo {
    pub node_id: String,
    pub node_type: NodeType,
    pub listen_ip: String,
    pub listen_port: u16,
    pub region: String,
    pub capabilities: NodeCapabilities,
    pub version: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeCapabilities {
    pub max_connections: u32,
    pub max_bandwidth_mbps: u32,
    pub supported_codecs: Vec<String>,
    pub supported_protocols: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NodeStatus {
    Starting,
    Online,
    Degraded,
    Offline,
    Maintenance,
}
```

### 6.2 会话相关数据结构
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionInfo {
    pub session_id: String,
    pub session_type: SessionType,
    pub status: SessionStatus,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub config: SessionConfig,
    pub stats: SessionStats,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SessionType {
    Transmitter,
    Receiver,
    Relay,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SessionStatus {
    Initializing,
    Active,
    Paused,
    Error,
    Terminated,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionStats {
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
    pub packet_loss_rate: f32,
    pub rtt_ms: u32,
    pub bandwidth_usage_mbps: f32,
}
```

### 6.3 传输相关数据结构
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TransportConfig {
    pub transport_type: TransportType,
    pub addresses: Vec<AddressConfig>,
    pub security: SecurityConfig,
    pub qos: QosConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TransportType {
    MediaSoup,
    PortForward,
    Multicast,
    WebRTC,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AddressConfig {
    pub ip: String,
    pub port: PortRange,
    pub protocol: Protocol,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PortRange {
    Single(u16),
    Range(u16, u16),
    List(Vec<u16>),
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Protocol {
    TCP,
    UDP,
    Multicast,
    WebRTC,
}
```

## 7. 状态管理机制

### 7.1 状态管理架构
```mermaid
stateDiagram-v2
    [*] --> Initializing : 节点启动
    Initializing --> Registering : 初始化完成
    Registering --> Online : 注册成功
    Registering --> Error : 注册失败
    Online --> Degraded : 部分服务异常
    Online --> Maintenance : 主动维护
    Degraded --> Online : 恢复正常
    Degraded --> Offline : 服务全部异常
    Maintenance --> Online : 维护完成
    Offline --> Initializing : 重启恢复
    Error --> Initializing : 重试启动
    Error --> [*] : 彻底失败
```

### 7.2 会话状态转换
```mermaid
stateDiagram-v2
    [*] --> Creating : 创建会话请求
    Creating --> Configuring : 基础验证通过
    Configuring --> Connecting : 配置就绪
    Connecting --> Active : 连接建立
    Connecting --> Failed : 连接失败
    Active --> Paused : 暂停传输
    Active --> Degraded : 部分连接异常
    Paused --> Active : 恢复传输
    Degraded --> Active : 连接恢复
    Degraded --> Failed : 连接完全失败
    Failed --> Terminated : 清理资源
    Active --> Terminated : 正常结束
    Paused --> Terminated : 强制结束
    Terminated --> [*]
```

## 8. 错误处理机制

### 8.1 错误分类和处理策略
```rust
#[derive(Debug, thiserror::Error)]
pub enum ForwarderError {
    #[error("节点管理错误: {0}")]
    NodeError(#[from] NodeError),
    
    #[error("会话管理错误: {0}")]
    SessionError(#[from] SessionError),
    
    #[error("传输错误: {0}")]
    TransportError(#[from] TransportError),
    
    #[error("连接错误: {0}")]
    ConnectionError(#[from] ConnectionError),
    
    #[error("配置错误: {0}")]
    ConfigError(#[from] ConfigError),
    
    #[error("网络错误: {0}")]
    NetworkError(#[from] NetworkError),
    
    #[error("MediaSoup错误: {0}")]
    MediaSoupError(String),
}

#[derive(Debug, thiserror::Error)]
pub enum NodeError {
    #[error("注册失败: {reason}")]
    RegistrationFailed { reason: String },
    
    #[error("心跳超时")]
    HeartbeatTimeout,
    
    #[error("配置无效: {field}")]
    InvalidConfig { field: String },
    
    #[error("资源不足: {resource}")]
    ResourceExhausted { resource: String },
}
```

### 8.2 错误恢复策略
```mermaid
flowchart TD
    A[检测到错误] --> B{错误类型判断}
    B -->|网络错误| C[重试连接]
    B -->|配置错误| D[加载默认配置]
    B -->|资源不足| E[清理过期资源]
    B -->|MediaSoup错误| F[重启MediaSoup Worker]
    B -->|系统错误| G[节点重启]
    
    C --> H{重试成功?}
    H -->|是| I[恢复正常]
    H -->|否| J[降级服务]
    
    D --> K{配置有效?}
    K -->|是| I
    K -->|否| L[使用最小配置]
    
    E --> M{资源释放成功?}
    M -->|是| I
    M -->|否| N[强制清理]
    
    F --> O{重启成功?}
    O -->|是| I
    O -->|否| P[禁用MediaSoup]
    
    G --> Q{重启成功?}
    Q -->|是| I
    Q -->|否| R[标记离线]
    
    J --> S[记录错误日志]
    L --> S
    N --> S
    P --> S
    R --> S
    S --> T[通知管理服务器]
```

## 9. 性能优化策略

### 9.1 内存管理
- **零拷贝传输**：使用`tokio`的零拷贝机制减少内存拷贝
- **对象池**：复用频繁创建销毁的对象，如连接、缓冲区等
- **内存预分配**：预先分配大块内存，减少运行时分配开销
- **垃圾回收**：定期清理过期的会话和连接对象

### 9.2 网络优化
- **连接复用**：对于相同目标的多个会话，复用底层连接
- **带宽控制**：实现流量整形，避免网络拥塞
- **自适应码率**：根据网络状况动态调整传输码率
- **多路径传输**：支持同时使用多个网络路径提高可靠性

### 9.3 并发优化
```rust
// 异步任务池设计
pub struct TaskPool {
    cpu_pool: ThreadPool,           // CPU密集型任务
    io_pool: ThreadPool,            // IO密集型任务
    mediasoup_pool: ThreadPool,     // MediaSoup相关任务
}

// 无锁数据结构
pub struct LockFreeSessionStore {
    sessions: Arc<DashMap<SessionId, SessionInfo>>,
    stats: Arc<AtomicStatsCollector>,
}
```

## 10. 安全机制

转发节点的安全机制基于**双重身份认证模式**，只接受来自转发管理服务器和其他转发节点的调用，不直接处理客户端请求。

### 10.1 安全架构概述

```mermaid
graph TB
    subgraph "转发管理服务器"
        MS[转发管理服务器]
        MS_CERT[管理服务器证书]
        MS_API[管理API]
    end
    
    subgraph "转发节点A"
        FN_A[转发节点A]
        SEC_A[安全管理器A]
        CERT_A[节点证书A]
    end
    
    subgraph "转发节点B"
        FN_B[转发节点B]
        SEC_B[安全管理器B]
        CERT_B[节点证书B]
    end
    
    MS -->|"1.管理调用(mTLS)"| FN_A
    MS -->|"2.管理调用(mTLS)"| FN_B
    FN_A -->|"3.节点间调用(mTLS)"| FN_B
    FN_B -->|"4.节点间调用(mTLS)"| FN_A
    
    MS_CERT -.->|"证书验证"| SEC_A
    MS_CERT -.->|"证书验证"| SEC_B
    CERT_A -.->|"证书验证"| SEC_B
    CERT_B -.->|"证书验证"| SEC_A
    
    style MS fill:#e1f5fe
    style FN_A fill:#e8f5e8
    style FN_B fill:#e8f5e8
```

### 7.2 认证和授权机制

#### 7.2.1 安全管理器设计
```rust
#[derive(Debug, Clone)]
pub struct SecurityManager {
    // 证书和密钥管理
    node_certificate: Certificate,
    node_private_key: PrivateKey,
    ca_certificate: Certificate,
    
    // 信任的调用方配置
    trusted_management_servers: Vec<TrustedServer>,
    trusted_nodes: Arc<DashMap<String, TrustedNode>>,
    
    // 访问控制
    access_control: AccessControlList,
    rate_limiter: RateLimiter,
}

impl SecurityManager {
    /// 验证管理服务器调用
    pub async fn verify_management_call(&self, client_cert: &Certificate, request_path: &str) -> Result<(), SecurityError> {
        // 1. 验证客户端证书是否为信任的管理服务器
        let server_info = self.verify_management_certificate(client_cert)?;
        
        // 2. 检查API路径权限
        if !server_info.has_permission(request_path) {
            return Err(SecurityError::InsufficientPermissions);
        }
        
        // 3. 应用访问控制策略
        self.check_access_control(&server_info.server_id, request_path)?;
        
        Ok(())
    }
    
    /// 验证节点间调用
    pub async fn verify_node_call(&self, client_cert: &Certificate, source_node_id: &str, request_path: &str) -> Result<(), SecurityError> {
        // 1. 验证客户端证书是否为信任的节点
        let node_info = self.verify_node_certificate(client_cert, source_node_id)?;
        
        // 2. 检查节点是否在线且可信
        if !node_info.is_active {
            return Err(SecurityError::NodeInactive);
        }
        
        // 3. 检查API路径权限
        if !self.is_inter_node_api(request_path) {
            return Err(SecurityError::UnauthorizedEndpoint);
        }
        
        // 4. 应用速率限制
        self.rate_limiter.check_rate_limit(source_node_id)?;
        
        Ok(())
    }
    
    /// 更新信任节点列表
    pub async fn update_trusted_nodes(&self, nodes: Vec<TrustedNode>) -> Result<(), SecurityError> {
        self.trusted_nodes.clear();
        for node in nodes {
            self.trusted_nodes.insert(node.node_id.clone(), node);
        }
        tracing::info!("Updated trusted nodes list");
        Ok(())
    }
    
    /// 检查证书有效性
    fn verify_certificate(&self, cert: &Certificate) -> Result<CertificateInfo, SecurityError> {
        // 1. 验证证书签名
        if !cert.verify_with_ca(&self.ca_certificate) {
            return Err(SecurityError::InvalidCertificate);
        }
        
        // 2. 检查证书有效期
        if cert.is_expired() {
            return Err(SecurityError::CertificateExpired);
        }
        
        // 3. 提取证书信息
        Ok(CertificateInfo {
            subject: cert.subject(),
            serial_number: cert.serial_number(),
            valid_until: cert.not_after(),
        })
    }
}
```

#### 7.2.2 认证流程
```mermaid
sequenceDiagram
    participant MS as 转发管理服务器
    participant FN_A as 转发节点A
    participant FN_B as 转发节点B

    Note over MS,FN_B: 管理服务器调用流程
    MS->>+FN_A: 管理API调用 (mTLS)
    FN_A->>FN_A: 验证管理服务器证书
    FN_A->>FN_A: 检查API权限
    FN_A->>FN_A: 应用访问控制
    FN_A-->>-MS: 返回结果
    
    Note over FN_A,FN_B: 节点间调用流程
    FN_A->>+FN_B: 节点间API调用 (mTLS)
    FN_B->>FN_B: 验证节点证书
    FN_B->>FN_B: 检查节点状态
    FN_B->>FN_B: 验证API权限
    FN_B->>FN_B: 应用速率限制
    FN_B-->>-FN_A: 返回结果
```

### 7.3 数据结构定义

#### 7.3.1 信任关系相关
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustedServer {
    pub server_id: String,
    pub server_name: String,
    pub certificate_fingerprint: String,
    pub allowed_apis: Vec<String>,     // 允许调用的API路径
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TrustedNode {
    pub node_id: String,
    pub node_name: String,
    pub region: String,
    pub certificate_fingerprint: String,
    pub is_active: bool,
    pub last_seen: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CertificateInfo {
    pub subject: String,
    pub serial_number: String,
    pub valid_until: DateTime<Utc>,
}
```

#### 7.3.2 节点认证凭据
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NodeCredentials {
    pub node_id: String,
    pub node_secret: String,       // 用于生成客户端证书的密钥
    pub region: String,
    pub certificate_path: String,
    pub private_key_path: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Certificate {
    pub pem_data: String,
    pub fingerprint: String,
    pub subject: String,
    pub issuer: String,
    pub not_before: DateTime<Utc>,
    pub not_after: DateTime<Utc>,
}

impl Certificate {
    pub fn verify_with_ca(&self, ca_cert: &Certificate) -> bool {
        // 使用CA证书验证当前证书的签名
        // 实际实现需要使用openssl或rustls库
        true // 简化示例
    }
    
    pub fn is_expired(&self) -> bool {
        Utc::now() > self.not_after
    }
    
    pub fn matches_fingerprint(&self, expected: &str) -> bool {
        self.fingerprint == expected
    }
}
```

### 7.4 数据加密

#### 7.4.1 传输层加密
- **mTLS双向认证**：所有API调用都使用mTLS进行双向认证
- **媒体流加密**：MediaSoup流使用SRTP/SRTCP加密
- **证书管理**：支持证书自动轮换和吊销列表检查

#### 7.4.2 加密配置
```rust
#[derive(Debug, Clone)]
pub struct EncryptionConfig {
    pub tls_config: TlsConfig,
    pub srtp_config: SrtpConfig,
    pub certificate_rotation_days: u32,
}

#[derive(Debug, Clone)]
pub struct TlsConfig {
    pub min_tls_version: TlsVersion,
    pub cipher_suites: Vec<CipherSuite>,
    pub client_cert_required: bool,    // 强制客户端证书
    pub verify_client_cert: bool,      // 验证客户端证书
}

#[derive(Debug, Clone)]
pub struct SrtpConfig {
    pub crypto_suite: String,          // 默认: AES_CM_128_HMAC_SHA1_80
    pub key_derivation_rate: u32,
}

#[derive(Debug, Clone)]
pub enum TlsVersion {
    V1_2,
    V1_3,
}
```

### 7.5 访问控制

#### 7.5.1 API权限控制
```rust
#[derive(Debug, Clone)]
pub enum ApiCategory {
    ManagementOnly,        // 仅管理服务器可调用
    InterNodeOnly,         // 仅节点间可调用
    Both,                  // 两者都可调用
}

#[derive(Debug, Clone)]
pub struct ApiPermission {
    pub path: String,
    pub method: HttpMethod,
    pub category: ApiCategory,
    pub rate_limit: Option<RateLimit>,
}

#[derive(Debug, Clone)]
pub struct AccessControlList {
    api_permissions: Vec<ApiPermission>,
    ip_whitelist: Vec<IpNetwork>,
    rate_limits: HashMap<String, RateLimit>,
}

impl AccessControlList {
    pub fn check_api_permission(&self, path: &str, method: &HttpMethod, caller_type: &CallerType) -> Result<(), SecurityError> {
        let permission = self.find_api_permission(path, method)?;
        
        match (&permission.category, caller_type) {
            (ApiCategory::ManagementOnly, CallerType::ManagementServer) => Ok(()),
            (ApiCategory::InterNodeOnly, CallerType::ForwarderNode) => Ok(()),
            (ApiCategory::Both, _) => Ok(()),
            _ => Err(SecurityError::UnauthorizedApiAccess),
        }
    }
    
    pub fn check_rate_limit(&self, caller_id: &str, api_path: &str) -> Result<(), SecurityError> {
        if let Some(limit) = self.get_rate_limit(api_path) {
            limit.check(caller_id)?;
        }
        Ok(())
    }
}

#[derive(Debug, Clone)]
pub enum CallerType {
    ManagementServer,
    ForwarderNode,
}

#[derive(Debug, Clone)]
pub enum HttpMethod {
    GET,
    POST,
    PUT,
    DELETE,
}
```

### 7.6 安全审计

#### 7.6.1 安全事件记录
```rust
#[derive(Debug, Clone, Serialize)]
pub struct SecurityEvent {
    pub event_id: String,
    pub event_type: SecurityEventType,
    pub timestamp: DateTime<Utc>,
    pub caller_info: CallerInfo,
    pub api_path: String,
    pub action: String,
    pub result: SecurityResult,
    pub details: serde_json::Value,
}

#[derive(Debug, Clone, Serialize)]
pub enum SecurityEventType {
    CertificateVerification,       // 证书验证
    AccessDenied,                 // 访问被拒绝
    RateLimitExceeded,            // 速率限制超出
    SuspiciousActivity,           // 可疑活动
    CertificateExpired,           // 证书过期
    UnauthorizedApiCall,          // 未授权的API调用
}

#[derive(Debug, Clone, Serialize)]
pub struct CallerInfo {
    pub caller_id: String,
    pub caller_type: CallerType,
    pub source_ip: IpAddr,
    pub certificate_fingerprint: Option<String>,
}

#[derive(Debug, Clone, Serialize)]
pub enum SecurityResult {
    Success,
    Failure(String),              // 失败原因
    Warning(String),              // 警告信息
}
```

#### 7.6.2 审计日志管理
```rust
pub struct SecurityAuditor {
    event_store: Arc<dyn EventStore>,
    alert_manager: AlertManager,
    metrics: SecurityMetrics,
}

impl SecurityAuditor {
    pub async fn record_event(&self, event: SecurityEvent) -> Result<(), AuditError> {
        // 记录事件
        self.event_store.store_event(&event).await?;
        
        // 更新指标
        self.metrics.record_security_event(&event);
        
        // 检查是否需要告警
        if self.should_alert(&event) {
            self.alert_manager.send_security_alert(&event).await?;
        }
        
        // 记录结构化日志
        tracing::warn!(
            event_type = ?event.event_type,
            caller_id = %event.caller_info.caller_id,
            api_path = %event.api_path,
            result = ?event.result,
            "Security event recorded"
        );
        
        Ok(())
    }
    
    fn should_alert(&self, event: &SecurityEvent) -> bool {
        matches!(event.event_type, 
            SecurityEventType::AccessDenied | 
            SecurityEventType::SuspiciousActivity |
            SecurityEventType::UnauthorizedApiCall |
            SecurityEventType::CertificateExpired
        ) && matches!(event.result, SecurityResult::Failure(_))
    }
}
```

### 7.7 安全配置示例

```toml
[security]
# 节点证书配置
node_certificate_path = "/etc/ssl/certs/forwarder-node.pem"
node_private_key_path = "/etc/ssl/private/forwarder-node.key"
ca_certificate_path = "/etc/ssl/certs/ca.pem"

# 证书管理
certificate_rotation_days = 90
certificate_check_interval = "1h"

[security.trusted_servers]
# 信任的管理服务器列表
[[security.trusted_servers.list]]
server_id = "management-server-001"
server_name = "Primary Management Server"
certificate_fingerprint = "sha256:abcd1234..."
allowed_apis = [
    "/api/v1/management/*",
    "/api/v1/status",
    "/api/v1/sessions/*"
]

[security.api_permissions]
# API权限配置
management_only_apis = [
    "POST /api/v1/sessions/transmitter",
    "DELETE /api/v1/sessions/transmitter/*",
    "POST /api/v1/sessions/receiver",
    "DELETE /api/v1/sessions/receiver/*",
    "GET /api/v1/sessions"
]

inter_node_only_apis = [
    "POST /api/v1/inter-node/access-request",
    "GET /api/v1/inter-node/status"
]

both_allowed_apis = [
    "GET /api/v1/status",
    "GET /health"
]

[security.tls]
min_tls_version = "1.3"
cipher_suites = [
    "TLS_AES_256_GCM_SHA384",
    "TLS_AES_128_GCM_SHA256"
]
client_cert_required = true
verify_client_cert = true

[security.audit]
enabled = true
log_level = "warn"
event_retention_days = 90
alert_on_failures = true
suspicious_activity_threshold = 10  # 连续失败次数告警阈值
```

## 8. 配置管理

### 8.1 配置层次结构
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ForwarderConfig {
    pub node: NodeConfig,
    pub network: NetworkConfig,
    pub mediasoup: MediaSoupConfig,
    pub security: SecurityConfig,
    pub logging: LoggingConfig,
    pub monitoring: MonitoringConfig,
}

// 支持热重载的配置管理器
pub struct ConfigManager {
    current_config: Arc<RwLock<ForwarderConfig>>,
    config_watchers: Vec<Box<dyn ConfigWatcher>>,
}
```

### 8.2 动态配置更新
```mermaid
sequenceDiagram
    participant MS as 转发管理服务器
    participant CM as 配置管理器
    participant NM as 节点管理器
    participant TM as 传输管理器

    MS->>CM: 推送配置更新
    CM->>CM: 验证配置有效性
    CM->>CM: 备份当前配置
    
    par 并行更新各模块
        CM->>NM: 更新节点配置
        CM->>TM: 更新传输配置
    end
    
    alt 更新成功
        CM->>MS: 确认配置应用成功
    else 更新失败
        CM->>CM: 回滚到备份配置
        CM->>MS: 报告配置应用失败
    end
```

## 11. 数据存储设计

### 11.1 存储架构概述

转发节点采用分层存储架构，根据数据特性和访问模式选择合适的存储方案：

```mermaid
graph TB
    subgraph "应用层"
        APP[转发节点应用]
    end

    subgraph "缓存层"
        L1[L1缓存<br/>内存缓存]
        L2[L2缓存<br/>Redis缓存]
    end

    subgraph "持久化层"
        MEM[内存存储<br/>会话数据]
        DISK[磁盘存储<br/>配置/日志]
        ETCD[分布式存储<br/>集群状态]
    end

    APP --> L1
    L1 --> L2
    L2 --> MEM
    L2 --> DISK
    L2 --> ETCD

    style L1 fill:#e3f2fd
    style L2 fill:#e8f5e8
    style MEM fill:#fff3e0
    style DISK fill:#fce4ec
    style ETCD fill:#f3e5f5
```

### 11.2 数据分类和存储策略

#### 11.2.1 数据分类

| 数据类型 | 特性 | 存储方案 | 持久化 | 一致性要求 |
|---------|------|----------|--------|------------|
| 会话状态 | 高频读写、临时性 | 内存 + Redis | 否 | 最终一致性 |
| 节点配置 | 低频写、高频读 | 内存 + 磁盘 | 是 | 强一致性 |
| 监控指标 | 高频写、定期读 | 内存 + 时序DB | 是 | 最终一致性 |
| 日志数据 | 高频写、低频读 | 磁盘 + 外部存储 | 是 | 最终一致性 |
| 集群状态 | 低频写、高频读 | etcd | 是 | 强一致性 |

#### 11.2.2 存储层设计

```rust
#[derive(Debug, Clone)]
pub struct StorageManager {
    // 内存存储 - 会话数据
    memory_store: Arc<MemoryStore>,

    // 缓存层 - Redis
    cache_manager: Arc<CacheManager>,

    // 持久化存储 - 配置和日志
    persistent_store: Arc<PersistentStore>,

    // 分布式存储 - 集群状态
    distributed_store: Arc<DistributedStore>,
}

impl StorageManager {
    pub async fn new(config: StorageConfig) -> Result<Self, StorageError> {
        let memory_store = Arc::new(MemoryStore::new());
        let cache_manager = Arc::new(CacheManager::new(&config.redis).await?);
        let persistent_store = Arc::new(PersistentStore::new(&config.disk).await?);
        let distributed_store = Arc::new(DistributedStore::new(&config.etcd).await?);

        Ok(Self {
            memory_store,
            cache_manager,
            persistent_store,
            distributed_store,
        })
    }
}
```

### 11.3 内存存储设计

#### 11.3.1 会话数据存储

```rust
use dashmap::DashMap;
use arc_swap::ArcSwap;

#[derive(Debug)]
pub struct MemoryStore {
    // 会话信息存储
    sessions: DashMap<SessionId, SessionInfo>,

    // 传输连接存储
    transports: DashMap<TransportId, TransportInfo>,

    // 节点间连接存储
    inter_node_connections: DashMap<ConnectionId, InterNodeConnection>,

    // 统计信息存储
    statistics: ArcSwap<NodeStatistics>,
}

impl MemoryStore {
    pub fn new() -> Self {
        Self {
            sessions: DashMap::new(),
            transports: DashMap::new(),
            inter_node_connections: DashMap::new(),
            statistics: ArcSwap::new(Arc::new(NodeStatistics::default())),
        }
    }

    // 会话管理
    pub fn store_session(&self, session_id: SessionId, session: SessionInfo) {
        self.sessions.insert(session_id, session);
    }

    pub fn get_session(&self, session_id: &SessionId) -> Option<SessionInfo> {
        self.sessions.get(session_id).map(|entry| entry.clone())
    }

    pub fn remove_session(&self, session_id: &SessionId) -> Option<SessionInfo> {
        self.sessions.remove(session_id).map(|(_, session)| session)
    }

    pub fn list_sessions(&self) -> Vec<SessionSummary> {
        self.sessions
            .iter()
            .map(|entry| SessionSummary::from(entry.value()))
            .collect()
    }

    // 统计信息更新
    pub fn update_statistics<F>(&self, updater: F)
    where
        F: FnOnce(&mut NodeStatistics)
    {
        let current = self.statistics.load();
        let mut new_stats = (**current).clone();
        updater(&mut new_stats);
        self.statistics.store(Arc::new(new_stats));
    }
}
```

### 11.4 缓存管理设计

#### 11.4.1 Redis缓存层

```rust
use redis::{Client, Connection, AsyncCommands};

#[derive(Debug)]
pub struct CacheManager {
    client: Client,
    connection_pool: deadpool_redis::Pool,
    key_prefix: String,
}

impl CacheManager {
    pub async fn new(config: &RedisConfig) -> Result<Self, CacheError> {
        let client = Client::open(config.url.as_str())?;
        let pool_config = deadpool_redis::Config::from_url(&config.url);
        let connection_pool = pool_config.create_pool(Some(deadpool_redis::Runtime::Tokio1))?;

        Ok(Self {
            client,
            connection_pool,
            key_prefix: config.key_prefix.clone(),
        })
    }

    // 会话缓存
    pub async fn cache_session(&self, session_id: &SessionId, session: &SessionInfo, ttl: u64) -> Result<(), CacheError> {
        let mut conn = self.connection_pool.get().await?;
        let key = format!("{}:session:{}", self.key_prefix, session_id);
        let value = serde_json::to_string(session)?;

        conn.setex(key, ttl, value).await?;
        Ok(())
    }

    pub async fn get_cached_session(&self, session_id: &SessionId) -> Result<Option<SessionInfo>, CacheError> {
        let mut conn = self.connection_pool.get().await?;
        let key = format!("{}:session:{}", self.key_prefix, session_id);

        let value: Option<String> = conn.get(key).await?;
        match value {
            Some(json) => {
                let session = serde_json::from_str(&json)?;
                Ok(Some(session))
            }
            None => Ok(None),
        }
    }

    // 节点状态缓存
    pub async fn cache_node_status(&self, node_id: &str, status: &NodeStatus, ttl: u64) -> Result<(), CacheError> {
        let mut conn = self.connection_pool.get().await?;
        let key = format!("{}:node_status:{}", self.key_prefix, node_id);
        let value = serde_json::to_string(status)?;

        conn.setex(key, ttl, value).await?;
        Ok(())
    }

    // 分布式锁
    pub async fn acquire_lock(&self, lock_key: &str, ttl: u64) -> Result<bool, CacheError> {
        let mut conn = self.connection_pool.get().await?;
        let key = format!("{}:lock:{}", self.key_prefix, lock_key);
        let value = uuid::Uuid::new_v4().to_string();

        let result: bool = conn.set_nx_ex(key, value, ttl).await?;
        Ok(result)
    }

    pub async fn release_lock(&self, lock_key: &str) -> Result<(), CacheError> {
        let mut conn = self.connection_pool.get().await?;
        let key = format!("{}:lock:{}", self.key_prefix, lock_key);

        conn.del(key).await?;
        Ok(())
    }
}
```

### 11.5 持久化存储设计

#### 11.5.1 配置文件存储

```rust
use tokio::fs;
use serde_json;

#[derive(Debug)]
pub struct PersistentStore {
    base_path: PathBuf,
    config_path: PathBuf,
    log_path: PathBuf,
}

impl PersistentStore {
    pub async fn new(config: &DiskConfig) -> Result<Self, StorageError> {
        let base_path = PathBuf::from(&config.base_path);
        let config_path = base_path.join("config");
        let log_path = base_path.join("logs");

        // 确保目录存在
        fs::create_dir_all(&config_path).await?;
        fs::create_dir_all(&log_path).await?;

        Ok(Self {
            base_path,
            config_path,
            log_path,
        })
    }

    // 配置持久化
    pub async fn save_config(&self, config: &ForwarderConfig) -> Result<(), StorageError> {
        let config_file = self.config_path.join("forwarder.json");
        let config_json = serde_json::to_string_pretty(config)?;

        // 原子写入
        let temp_file = config_file.with_extension("tmp");
        fs::write(&temp_file, config_json).await?;
        fs::rename(temp_file, config_file).await?;

        Ok(())
    }

    pub async fn load_config(&self) -> Result<ForwarderConfig, StorageError> {
        let config_file = self.config_path.join("forwarder.json");
        let config_json = fs::read_to_string(config_file).await?;
        let config = serde_json::from_str(&config_json)?;
        Ok(config)
    }

    // 日志轮转
    pub async fn rotate_logs(&self) -> Result<(), StorageError> {
        let log_files = fs::read_dir(&self.log_path).await?;
        let mut files: Vec<_> = log_files.collect::<Result<Vec<_>, _>>().await?;

        // 按修改时间排序
        files.sort_by_key(|entry| {
            entry.metadata()
                .and_then(|m| m.modified())
                .unwrap_or(std::time::SystemTime::UNIX_EPOCH)
        });

        // 保留最近的10个日志文件
        if files.len() > 10 {
            for file in files.iter().take(files.len() - 10) {
                fs::remove_file(file.path()).await?;
            }
        }

        Ok(())
    }
}
```

### 11.6 分布式存储设计

#### 11.6.1 etcd集群状态管理

```rust
use etcd_rs::{Client, GetOptions, PutOptions, WatchOptions};

#[derive(Debug)]
pub struct DistributedStore {
    client: Client,
    key_prefix: String,
}

impl DistributedStore {
    pub async fn new(config: &EtcdConfig) -> Result<Self, StorageError> {
        let client = Client::connect(&config.endpoints, None).await?;

        Ok(Self {
            client,
            key_prefix: config.key_prefix.clone(),
        })
    }

    // 节点注册
    pub async fn register_node(&self, node_info: &NodeInfo) -> Result<(), StorageError> {
        let key = format!("{}/nodes/{}", self.key_prefix, node_info.node_id);
        let value = serde_json::to_string(node_info)?;

        // 使用租约确保节点离线时自动清理
        let lease = self.client.lease_grant(30, None).await?;
        let put_options = PutOptions::new().with_lease(lease.id());

        self.client.put(key, value, Some(put_options)).await?;

        // 定期续租
        tokio::spawn({
            let client = self.client.clone();
            let lease_id = lease.id();
            async move {
                let mut interval = tokio::time::interval(Duration::from_secs(10));
                loop {
                    interval.tick().await;
                    if client.lease_keep_alive(lease_id).await.is_err() {
                        break;
                    }
                }
            }
        });

        Ok(())
    }

    // 获取集群节点列表
    pub async fn get_cluster_nodes(&self) -> Result<Vec<NodeInfo>, StorageError> {
        let key = format!("{}/nodes/", self.key_prefix);
        let get_options = GetOptions::new().with_prefix();

        let response = self.client.get(key, Some(get_options)).await?;
        let mut nodes = Vec::new();

        for kv in response.kvs() {
            let node_info: NodeInfo = serde_json::from_slice(kv.value())?;
            nodes.push(node_info);
        }

        Ok(nodes)
    }

    // 监听集群变化
    pub async fn watch_cluster_changes(&self) -> Result<impl Stream<Item = ClusterEvent>, StorageError> {
        let key = format!("{}/nodes/", self.key_prefix);
        let watch_options = WatchOptions::new().with_prefix();

        let (watcher, stream) = self.client.watch(key, Some(watch_options)).await?;

        let event_stream = stream.map(|watch_response| {
            let events: Vec<ClusterEvent> = watch_response
                .events()
                .iter()
                .filter_map(|event| {
                    match event.event_type() {
                        etcd_rs::EventType::Put => {
                            let node_info: NodeInfo = serde_json::from_slice(event.kv().value()).ok()?;
                            Some(ClusterEvent::NodeJoined(node_info))
                        }
                        etcd_rs::EventType::Delete => {
                            let key = String::from_utf8_lossy(event.kv().key());
                            let node_id = key.split('/').last()?.to_string();
                            Some(ClusterEvent::NodeLeft(node_id))
                        }
                    }
                })
                .collect();

            futures::stream::iter(events)
        })
        .flatten();

        Ok(event_stream)
    }

    // 分布式配置管理
    pub async fn store_global_config(&self, config: &GlobalConfig) -> Result<(), StorageError> {
        let key = format!("{}/config/global", self.key_prefix);
        let value = serde_json::to_string(config)?;

        self.client.put(key, value, None).await?;
        Ok(())
    }

    pub async fn get_global_config(&self) -> Result<Option<GlobalConfig>, StorageError> {
        let key = format!("{}/config/global", self.key_prefix);

        let response = self.client.get(key, None).await?;
        if let Some(kv) = response.kvs().first() {
            let config: GlobalConfig = serde_json::from_slice(kv.value())?;
            Ok(Some(config))
        } else {
            Ok(None)
        }
    }
}

#[derive(Debug, Clone)]
pub enum ClusterEvent {
    NodeJoined(NodeInfo),
    NodeLeft(String),
    ConfigUpdated(GlobalConfig),
}
```

### 11.7 数据一致性保证

#### 11.7.1 一致性策略

```rust
#[derive(Debug)]
pub struct ConsistencyManager {
    storage: Arc<StorageManager>,
    consistency_level: ConsistencyLevel,
}

#[derive(Debug, Clone)]
pub enum ConsistencyLevel {
    Eventual,    // 最终一致性
    Strong,      // 强一致性
    Causal,      // 因果一致性
}

impl ConsistencyManager {
    // 会话数据同步
    pub async fn sync_session_data(&self, session_id: &SessionId) -> Result<(), ConsistencyError> {
        match self.consistency_level {
            ConsistencyLevel::Eventual => {
                // 异步同步到缓存
                self.async_sync_to_cache(session_id).await?;
            }
            ConsistencyLevel::Strong => {
                // 同步写入所有存储层
                self.sync_to_all_stores(session_id).await?;
            }
            ConsistencyLevel::Causal => {
                // 基于向量时钟的因果一致性
                self.causal_sync(session_id).await?;
            }
        }
        Ok(())
    }

    async fn async_sync_to_cache(&self, session_id: &SessionId) -> Result<(), ConsistencyError> {
        if let Some(session) = self.storage.memory_store.get_session(session_id) {
            tokio::spawn({
                let cache = self.storage.cache_manager.clone();
                let session_id = session_id.clone();
                let session = session.clone();
                async move {
                    let _ = cache.cache_session(&session_id, &session, 3600).await;
                }
            });
        }
        Ok(())
    }

    async fn sync_to_all_stores(&self, session_id: &SessionId) -> Result<(), ConsistencyError> {
        if let Some(session) = self.storage.memory_store.get_session(session_id) {
            // 同时写入缓存和分布式存储
            let cache_result = self.storage.cache_manager.cache_session(session_id, &session, 3600);
            let distributed_result = self.sync_to_distributed_store(session_id, &session);

            let (cache_res, dist_res) = tokio::join!(cache_result, distributed_result);
            cache_res?;
            dist_res?;
        }
        Ok(())
    }

    async fn sync_to_distributed_store(&self, session_id: &SessionId, session: &SessionInfo) -> Result<(), ConsistencyError> {
        let key = format!("sessions/{}", session_id);
        let value = serde_json::to_string(session)?;

        // 使用etcd的事务确保原子性
        let txn = etcd_rs::TxnRequest::new()
            .when(vec![etcd_rs::Compare::version(key.clone(), etcd_rs::CompareOp::Greater, 0)])
            .and_then(vec![etcd_rs::TxnOp::put(key.clone(), value.clone(), None)])
            .or_else(vec![etcd_rs::TxnOp::put(key, value, None)]);

        self.storage.distributed_store.client.txn(txn).await?;
        Ok(())
    }
}
```

### 11.8 存储配置

#### 11.8.1 存储配置结构

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    pub memory: MemoryConfig,
    pub redis: RedisConfig,
    pub disk: DiskConfig,
    pub etcd: EtcdConfig,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MemoryConfig {
    pub max_sessions: usize,
    pub max_transports: usize,
    pub cleanup_interval_seconds: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RedisConfig {
    pub url: String,
    pub key_prefix: String,
    pub max_connections: u32,
    pub connection_timeout_ms: u64,
    pub default_ttl_seconds: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskConfig {
    pub base_path: String,
    pub max_log_size_mb: u64,
    pub log_retention_days: u32,
    pub sync_interval_seconds: u64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EtcdConfig {
    pub endpoints: Vec<String>,
    pub key_prefix: String,
    pub lease_ttl_seconds: u64,
    pub keepalive_interval_seconds: u64,
}
```

#### 11.8.2 配置示例

```toml
[storage.memory]
max_sessions = 10000
max_transports = 50000
cleanup_interval_seconds = 300

[storage.redis]
url = "redis://localhost:6379"
key_prefix = "kvm_tunnel"
max_connections = 100
connection_timeout_ms = 5000
default_ttl_seconds = 3600

[storage.disk]
base_path = "/var/lib/kvm_tunnel"
max_log_size_mb = 100
log_retention_days = 30
sync_interval_seconds = 60

[storage.etcd]
endpoints = ["http://etcd1:2379", "http://etcd2:2379", "http://etcd3:2379"]
key_prefix = "/kvm_tunnel"
lease_ttl_seconds = 30
keepalive_interval_seconds = 10
```

## 12. 监控和日志

### 9.1 监控指标
```rust
#[derive(Debug, Clone, Serialize)]
pub struct NodeMetrics {
    pub system: SystemMetrics,
    pub network: NetworkMetrics,
    pub sessions: SessionMetrics,
    pub performance: PerformanceMetrics,
    pub security: SecurityMetrics,
}

#[derive(Debug, Clone, Serialize)]
pub struct SystemMetrics {
    pub cpu_usage: f32,
    pub memory_usage: f32,
    pub disk_usage: f32,
    pub uptime_seconds: u64,
}

#[derive(Debug, Clone, Serialize)]
pub struct NetworkMetrics {
    pub bandwidth_in_mbps: f32,
    pub bandwidth_out_mbps: f32,
    pub packet_loss_rate: f32,
    pub connection_count: u32,
}

#[derive(Debug, Clone, Serialize)]
pub struct SessionMetrics {
    pub total_sessions: u32,
    pub active_sessions: u32,
    pub failed_sessions: u32,
    pub average_session_duration: f32,
}

#[derive(Debug, Clone, Serialize)]
pub struct PerformanceMetrics {
    pub request_latency_ms: f32,
    pub token_verification_latency_ms: f32,
    pub session_creation_latency_ms: f32,
    pub throughput_mbps: f32,
}

#[derive(Debug, Clone, Serialize)]
pub struct SecurityMetrics {
    pub certificate_verifications: u32,
    pub certificate_verification_failures: u32,
    pub management_server_calls: u32,
    pub management_server_call_failures: u32,
    pub inter_node_calls: u32,
    pub inter_node_call_failures: u32,
    pub rate_limited_requests: u32,
    pub untrusted_callers: u32,
}
```

### 9.2 日志管理
```rust
// 结构化日志配置
pub struct LogManager {
    logger: slog::Logger,
    metrics_collector: MetricsCollector,
    alert_manager: AlertManager,
}

// 日志级别和格式
pub enum LogLevel {
    Trace,
    Debug,
    Info,
    Warn,
    Error,
    Fatal,
}

// 关键事件日志
pub struct EventLogger {
    session_logger: Logger,
    transport_logger: Logger,
    security_logger: Logger,
    performance_logger: Logger,
}
```

### 9.3 Prometheus指标配置
```rust
// src/monitoring/metrics.rs
use prometheus::{Counter, Gauge, Histogram, Registry};

pub struct ForwarderMetrics {
    pub sessions_total: Counter,
    pub sessions_active: Gauge,
    pub data_bytes_total: Counter,
    pub forwarding_latency: Histogram,
    pub error_count: Counter,
}

impl ForwarderMetrics {
    pub fn new(registry: &Registry) -> Self {
        let sessions_total = Counter::new(
            "forwarder_sessions_total",
            "Total number of sessions created"
        ).unwrap();
        
        let sessions_active = Gauge::new(
            "forwarder_sessions_active",
            "Number of currently active sessions"
        ).unwrap();
        
        // 注册指标...
        registry.register(Box::new(sessions_total.clone())).unwrap();
        registry.register(Box::new(sessions_active.clone())).unwrap();
        
        Self {
            sessions_total,
            sessions_active,
            // ... 其他指标
        }
    }
}
```

### 9.4 结构化日志配置
```rust
// src/logging/config.rs
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

pub fn init_logging() -> Result<(), Box<dyn std::error::Error>> {
    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "kvm_tunnel=info".into())
        )
        .with(tracing_subscriber::fmt::layer().json())
        .init();
    
    Ok(())
}

// 使用示例
#[tracing::instrument]
async fn create_session(config: SessionConfig) -> Result<SessionId, SessionError> {
    tracing::info!("Creating new session");
    
    match do_create_session(config).await {
        Ok(session_id) => {
            tracing::info!(
                session_id = %session_id,
                "Session created successfully"
            );
            Ok(session_id)
        }
        Err(e) => {
            tracing::error!(
                error = %e,
                "Failed to create session"
            );
            Err(e)
        }
    }
}
```

## 13. API设计

### 13.1 API分类概述

转发节点的API分为以下几个主要类别：

1. **节点管理API** - 与转发管理服务器的交互
2. **节点间通信API** - 与其他转发节点的交互  
3. **会话管理API** - 设备接入和会话管理
4. **监控和状态API** - 节点状态查询和监控

### 10.2 API认证机制

#### 10.2.1 双重身份认证
转发节点只接受两种调用方的请求：

1. **转发管理服务器调用**：
   - 使用mTLS双向认证
   - 验证管理服务器证书指纹
   - 检查API路径权限

2. **转发节点间调用**：
   - 使用mTLS双向认证
   - 验证节点证书指纹
   - 检查节点是否在信任列表中

#### 10.2.2 认证流程
```mermaid
sequenceDiagram
    participant MS as 转发管理服务器
    participant FN_A as 转发节点A
    participant FN_B as 转发节点B

    Note over MS,FN_A: 管理服务器调用
    MS->>+FN_A: HTTPS请求 + 客户端证书
    FN_A->>FN_A: 验证客户端证书
    FN_A->>FN_A: 检查证书指纹
    FN_A->>FN_A: 验证API权限
    FN_A-->>-MS: 返回结果

    Note over FN_A,FN_B: 节点间调用
    FN_A->>+FN_B: HTTPS请求 + 客户端证书
    FN_B->>FN_B: 验证客户端证书
    FN_B->>FN_B: 检查节点信任状态
    FN_B->>FN_B: 验证API权限
    FN_B-->>-FN_A: 返回结果
```

#### 10.2.3 API分类
- **管理API**：仅管理服务器可调用，用于会话管理、配置更新等
- **节点间API**：仅节点间可调用，用于跨节点数据转发
- **通用API**：两者都可调用，如状态查询、健康检查

### 10.3 节点管理API(转发管理服务器提供)

#### 10.2.1 节点注册
```http
POST /api/v1/management/register
```

**请求体：**
```json
{
  "node_id": "fn-001",
  "node_type": "forwarder",
  "listen_ip": "*************",
  "listen_port": 8080,
  "capabilities": {
    "max_connections": 1000,
  },
  "region": "guangzhou",
}
```

**响应：**
```json
{
  "status": "success",
  "node_id": "fn-001",
  "global_config": {
    "heartbeat_interval": 30,
    "session_timeout": 300,
  }
}
```

#### 10.2.2 心跳上报
```http
POST /api/v1/management/heartbeat
```

**请求体：**
```json
{
  "node_id": "fn-001",
  "timestamp": "2025-06-11T10:30:00Z",
  "status": "online",
  "load_info": {
    "cpu_usage": 45.2,
    "memory_usage": 68.5,
    "network_io": {
      "rx_bytes": 1048576000,
      "tx_bytes": 2097152000
    }
  }
}
```

**响应：**
```json
{
  "status": "success",
  "timestamp": "2025-06-11T10:30:01Z",
  "config_updates": {
    "log_level": "info",
  }
}
```

### 10.4 节点间通信API

> **认证要求**：使用mTLS双向认证，调用方必须是信任的转发节点

#### 10.4.1 请求跨节点访问
```http
POST /api/v1/inter-node/access-request
Content-Type: application/json
```

**请求体：**
```json
{
  "request_id": "req_12345",
  "source_node": "fn-001",
  "target_session_id": "sess_tx_67890",
  "transports": [
    {
      "address": "************",
      "port": 5000,
      "protocol": "multicast"
    },
    {
      "address": "************", 
      "port": [6000, 6001],
      "protocol": "tcp"
    },
    {
      "address": "************",
      "port": [7000, 7001],
      "protocol": "udp"
    }
  ]
}
```

**响应：**
```json
{
  "status": "success",
  "request_id": "req_12345",
  "session_id": "sess_67890",
  "transports": {
    "pipe_transport": {
      "ip": "**********",
      "port": 15000,
      "srtp_parameters": {
        "crypto_suite": "AES_CM_128_HMAC_SHA1_80",
        "key_base64": "base64_encoded_key"
      }
    },
    "tcp_tunnel": {
      "listen_port": 16000
    },
    "udp_tunnel": {
      "listen_port": 17000
    }
  }
}
```

#### 10.4.2 获取节点间连接状态
```http
GET /api/v1/inter-node/connections/{connection_id}
```

**响应：**
```json
{
  "connection_id": "conn_12345",
  "status": "active",
  "source_node": "fn-001",
  "target_node": "fn-002", 
  "created_at": "2025-06-11T09:30:00Z",
  "statistics": {
    "bytes_transferred": 1048576000,
    "packets_sent": 1000000,
    "packet_loss_rate": 0.01,
    "latency_ms": 15
  }
}
```

### 10.5 会话管理API

> **认证要求**：使用mTLS双向认证，调用方必须是信任的转发管理服务器

#### 10.5.1 创建KVM TX设备转发会话
```http
POST /api/v1/sessions/transmitter
Content-Type: application/json
```

**请求体：**
```json
{
  "session_id": "sess_tx_12345",
  "device_id": "kvm_tx_001", 
  "transports": [
    {
      "type": "media_forwarding",
      "addresses": [
        {
          "ip": "************",
          "port": [5000, 5001],
          "protocol": "multicast"
        }
      ]
    },
    {
      "type": "port_forwarding",
      "addresses": [
        {
          "ip": "************",
          "port": [6000, 6001],
          "protocol": "tcp"
        },
        {
          "ip": "************", 
          "port": [7000, 7001],
          "protocol": "udp"
        }
      ]
    }
  ]
}
```

**响应：**
```json
{
  "status": "success",
  "session_id": "sess_tx_12345"
}
```

#### 10.5.2 结束KVM TX设备转发会话
```http
DELETE /api/v1/sessions/transmitter/{session_id}
```

**响应：**
```json
{
  "status": "success",
  "session_id": "sess_tx_12345"
}
```

#### 10.5.3 创建KVM RX设备转发会话
```http
POST /api/v1/sessions/receiver
Content-Type: application/json
```
**请求体：**
```json
{
  "session_id": "sess_rx_67890",
  "device_id": "kvm_rx_001",
  "transmitter": {
    "session_id": "sess_tx_12345",
    "node_id": "fn-002",
    "connection_info": {
      "address": "*************",
      "port": 15000
    }
  }
}
```

**响应：**
```json
{
  "status": "success", 
  "session_id": "sess_rx_67890"
}
```

#### 10.5.4 结束KVM RX设备转发会话
```http
DELETE /api/v1/sessions/receiver/{session_id}
```

**响应：**
```json
{
  "status": "success",
  "session_id": "sess_rx_67890"
}
```

#### 10.5.5 获取会话状态
```http
GET /api/v1/sessions/{session_id}
```

**响应：**
```json
{
  "session_id": "sess_tx_12345",
  "session_type": "transmitter",
  "status": "active",
  "device_id": "kvm_tx_001",
  "created_at": "2025-06-11T09:30:00Z",
  "transports": [
    {
      "type": "media_forwarding",
      "addresses": [
        {
          "ip": "************",
          "port": [5000, 5001],
          "protocol": "multicast"
        }
      ]
    }
  ],
  "statistics": {
    "bytes_sent": 1048576000,
    "packets_sent": 1000000,
    "active_connections": 3
  }
}
```

#### 10.5.6 获取会话列表
```http
GET /api/v1/sessions
```
**响应：**
```json
{
  "sessions": [
    {
      "session_id": "sess_tx_12345",
      "type": "transmitter",
      "status": "active",
      "device_id": "kvm_tx_001",
      "created_at": "2025-06-11T09:30:00Z"
    },
    {
      "session_id": "sess_rx_67890", 
      "type": "receiver",
      "status": "active",
      "device_id": "kvm_rx_001",
      "created_at": "2025-06-11T09:35:00Z"
    }
  ],
  "total_count": 2
}
```

### 10.6 监控和状态API

> **认证要求**：使用mTLS双向认证，管理服务器和节点都可调用

#### 10.6.1 节点状态查询
```http
GET /api/v1/status
```

**响应：**
```json
{
  "node_id": "fn-001",
  "status": "online",
  "uptime": 86400,
  "version": "1.0.0",
  "system_info": {
    "cpu_cores": 8,
    "memory_total": 16777216000,
    "memory_available": **********,
    "network_interfaces": [
      {
        "name": "eth0",
        "ip": "*************",
        "status": "up"
      }
    ]
  },
  "load_metrics": {
    "cpu_usage": 45.2,
    "memory_usage": 68.5,
    "connection_count": 150,
    "bandwidth_usage": {
      "rx_mbps": 100.5,
      "tx_mbps": 200.8
    }
  }
}
```

#### 10.6.2 健康检查
```http
GET /health
```
> 此接口无需认证，用于负载均衡器健康检查

**响应：**
```json
{
  "status": "healthy",
  "timestamp": "2025-06-11T10:30:00Z"
}
```

### 10.7 错误响应格式

所有API在出错时都返回统一的错误格式：

```json
{
  "error": {
    "code": "INVALID_REQUEST",
    "message": "请求参数无效",
    "details": {
      "field": "target_id",
      "reason": "目标设备不存在"
    },
    "timestamp": "2025-06-11T10:30:00Z"
  }
}
```

常见错误代码：
- `INVALID_REQUEST` - 请求参数无效
- `UNAUTHORIZED` - 未授权访问（证书验证失败）
- `CERTIFICATE_EXPIRED` - 客户端证书已过期
- `CERTIFICATE_INVALID` - 客户端证书无效或不受信任
- `FORBIDDEN` - 权限不足（证书有效但无权限调用该API）
- `NOT_FOUND` - 资源不存在
- `CONFLICT` - 资源冲突
- `RATE_LIMITED` - 请求频率超限
- `CALLER_NOT_TRUSTED` - 调用方不在信任列表中
- `SESSION_LIMIT_EXCEEDED` - 会话数量超过限制
- `INTERNAL_ERROR` - 内部服务器错误
- `SERVICE_UNAVAILABLE` - 服务不可用
- `MEDIASOUP_ERROR` - mediasoup相关错误
- `NETWORK_ERROR` - 网络连接错误

#### 认证相关错误示例

**证书缺失：**
```json
{
  "error": {
    "code": "UNAUTHORIZED",
    "message": "客户端证书缺失",
    "details": {
      "required": "Client certificate required for mTLS authentication"
    },
    "timestamp": "2025-06-11T10:30:00Z"
  }
}
```

**证书过期：**
```json
{
  "error": {
    "code": "CERTIFICATE_EXPIRED", 
    "message": "客户端证书已过期",
    "details": {
      "expired_at": "2025-06-10T09:30:00Z",
      "current_time": "2025-06-11T10:30:00Z"
    },
    "timestamp": "2025-06-11T10:30:00Z"
  }
}
```

**调用方不受信任：**
```json
{
  "error": {
    "code": "CALLER_NOT_TRUSTED",
    "message": "调用方不在信任列表中",
    "details": {
      "certificate_fingerprint": "sha256:unknown_fingerprint",
      "caller_type": "unknown"
    },
    "timestamp": "2025-06-11T10:30:00Z"
  }
}
```

**API权限不足：**
```json
{
  "error": {
    "code": "FORBIDDEN",
    "message": "无权限调用此API",
    "details": {
      "api_path": "/api/v1/sessions/transmitter",
      "caller_type": "forwarder_node",
      "required_caller": "management_server"
    },
    "timestamp": "2025-06-11T10:30:00Z"
  }
}
```

## 15. 部署架构

### 15.1 部署架构概述

转发节点支持多种部署模式，从单节点部署到大规模分布式集群部署，满足不同场景的需求。

```mermaid
graph TB
    subgraph "负载均衡层"
        LB[负载均衡器<br/>Nginx/HAProxy]
    end

    subgraph "转发节点集群"
        subgraph "区域A"
            FN1[转发节点1<br/>容器化部署]
            FN2[转发节点2<br/>容器化部署]
        end

        subgraph "区域B"
            FN3[转发节点3<br/>容器化部署]
            FN4[转发节点4<br/>容器化部署]
        end
    end

    subgraph "基础设施层"
        subgraph "存储集群"
            REDIS[Redis集群<br/>缓存存储]
            ETCD[etcd集群<br/>配置存储]
        end

        subgraph "监控体系"
            PROM[Prometheus<br/>指标收集]
            GRAF[Grafana<br/>监控面板]
            ALERT[AlertManager<br/>告警管理]
        end

        subgraph "日志体系"
            ELK[ELK Stack<br/>日志收集分析]
        end
    end

    LB --> FN1
    LB --> FN2
    LB --> FN3
    LB --> FN4

    FN1 --> REDIS
    FN2 --> REDIS
    FN3 --> REDIS
    FN4 --> REDIS

    FN1 --> ETCD
    FN2 --> ETCD
    FN3 --> ETCD
    FN4 --> ETCD

    FN1 --> PROM
    FN2 --> PROM
    FN3 --> PROM
    FN4 --> PROM

    FN1 --> ELK
    FN2 --> ELK
    FN3 --> ELK
    FN4 --> ELK

    style LB fill:#ff9800
    style FN1 fill:#2196f3
    style FN2 fill:#2196f3
    style FN3 fill:#2196f3
    style FN4 fill:#2196f3
    style REDIS fill:#e91e63
    style ETCD fill:#9c27b0
    style PROM fill:#4caf50
    style GRAF fill:#ff5722
    style ELK fill:#795548
```

### 15.2 容器化部署

#### 15.2.1 Docker镜像构建

```dockerfile
# Dockerfile
FROM rust:1.70-slim as builder

WORKDIR /app
COPY . .

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libclang-dev \
    && rm -rf /var/lib/apt/lists/*

# 构建应用
RUN cargo build --release

# 运行时镜像
FROM debian:bookworm-slim

# 安装运行时依赖
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# 创建应用用户
RUN useradd -r -s /bin/false kvm_tunnel

# 复制二进制文件
COPY --from=builder /app/target/release/kvm_tunnel /usr/local/bin/
COPY --from=builder /app/config/ /etc/kvm_tunnel/

# 创建数据目录
RUN mkdir -p /var/lib/kvm_tunnel /var/log/kvm_tunnel && \
    chown -R kvm_tunnel:kvm_tunnel /var/lib/kvm_tunnel /var/log/kvm_tunnel

# 暴露端口
EXPOSE 8080 8443

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

USER kvm_tunnel
ENTRYPOINT ["/usr/local/bin/kvm_tunnel"]
CMD ["--config", "/etc/kvm_tunnel/production.toml"]
```

#### 15.2.2 多阶段构建优化

```dockerfile
# 优化的Dockerfile
FROM rust:1.70-alpine as chef
RUN cargo install cargo-chef
WORKDIR /app

FROM chef as planner
COPY . .
RUN cargo chef prepare --recipe-path recipe.json

FROM chef as builder
COPY --from=planner /app/recipe.json recipe.json
# 构建依赖（缓存层）
RUN cargo chef cook --release --recipe-path recipe.json

# 构建应用
COPY . .
RUN cargo build --release

# 运行时镜像
FROM alpine:3.18
RUN apk --no-cache add ca-certificates libgcc
WORKDIR /app

# 复制二进制文件
COPY --from=builder /app/target/release/kvm_tunnel /usr/local/bin/
COPY config/ /etc/kvm_tunnel/

# 创建非root用户
RUN addgroup -g 1001 kvm_tunnel && \
    adduser -D -s /bin/sh -u 1001 -G kvm_tunnel kvm_tunnel

# 创建数据目录
RUN mkdir -p /var/lib/kvm_tunnel /var/log/kvm_tunnel && \
    chown -R kvm_tunnel:kvm_tunnel /var/lib/kvm_tunnel /var/log/kvm_tunnel

EXPOSE 8080 8443
USER kvm_tunnel

ENTRYPOINT ["/usr/local/bin/kvm_tunnel"]
```

### 15.3 Kubernetes部署

#### 15.3.1 部署清单

```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kvm-tunnel-forwarder
  namespace: kvm-tunnel
  labels:
    app: kvm-tunnel-forwarder
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: kvm-tunnel-forwarder
  template:
    metadata:
      labels:
        app: kvm-tunnel-forwarder
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: kvm-tunnel-forwarder
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: forwarder
        image: kvm-tunnel/forwarder:v1.0.0
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        - name: https
          containerPort: 8443
          protocol: TCP
        - name: metrics
          containerPort: 9090
          protocol: TCP
        env:
        - name: RUST_LOG
          value: "info"
        - name: NODE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: config
          mountPath: /etc/kvm_tunnel
          readOnly: true
        - name: certs
          mountPath: /etc/ssl/certs/kvm_tunnel
          readOnly: true
        - name: data
          mountPath: /var/lib/kvm_tunnel
        - name: logs
          mountPath: /var/log/kvm_tunnel
      volumes:
      - name: config
        configMap:
          name: kvm-tunnel-config
      - name: certs
        secret:
          secretName: kvm-tunnel-certs
      - name: data
        emptyDir: {}
      - name: logs
        emptyDir: {}
      nodeSelector:
        node-type: compute
      tolerations:
      - key: "node-type"
        operator: "Equal"
        value: "compute"
        effect: "NoSchedule"
```

#### 15.3.2 服务配置

```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: kvm-tunnel-forwarder
  namespace: kvm-tunnel
  labels:
    app: kvm-tunnel-forwarder
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8080
    targetPort: 8080
    protocol: TCP
  - name: https
    port: 8443
    targetPort: 8443
    protocol: TCP
  selector:
    app: kvm-tunnel-forwarder

---
# 用于节点间通信的Headless Service
apiVersion: v1
kind: Service
metadata:
  name: kvm-tunnel-forwarder-headless
  namespace: kvm-tunnel
  labels:
    app: kvm-tunnel-forwarder
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: https
    port: 8443
    targetPort: 8443
    protocol: TCP
  selector:
    app: kvm-tunnel-forwarder

---
# 外部访问的LoadBalancer Service
apiVersion: v1
kind: Service
metadata:
  name: kvm-tunnel-forwarder-external
  namespace: kvm-tunnel
  labels:
    app: kvm-tunnel-forwarder
spec:
  type: LoadBalancer
  loadBalancerIP: **********
  ports:
  - name: https
    port: 443
    targetPort: 8443
    protocol: TCP
  selector:
    app: kvm-tunnel-forwarder
```

#### 15.3.3 配置管理

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: kvm-tunnel-config
  namespace: kvm-tunnel
data:
  production.toml: |
    [node]
    node_id = "${NODE_ID}"
    listen_ip = "${POD_IP}"
    listen_port = 8080
    region = "kubernetes-cluster"

    [network]
    bind_address = "0.0.0.0:8080"
    tls_bind_address = "0.0.0.0:8443"

    [storage.redis]
    url = "redis://redis-cluster:6379"
    key_prefix = "kvm_tunnel"

    [storage.etcd]
    endpoints = ["http://etcd-cluster:2379"]
    key_prefix = "/kvm_tunnel"

    [monitoring]
    metrics_bind_address = "0.0.0.0:9090"

    [logging]
    level = "info"
    format = "json"

---
# 证书Secret
apiVersion: v1
kind: Secret
metadata:
  name: kvm-tunnel-certs
  namespace: kvm-tunnel
type: Opaque
data:
  tls.crt: LS0tLS1CRUdJTi... # base64编码的证书
  tls.key: LS0tLS1CRUdJTi... # base64编码的私钥
  ca.crt: LS0tLS1CRUdJTi...  # base64编码的CA证书
```

### 15.4 高可用部署

#### 15.4.1 多区域部署

```yaml
# 区域A部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kvm-tunnel-forwarder-region-a
  namespace: kvm-tunnel
spec:
  replicas: 2
  template:
    spec:
      nodeSelector:
        topology.kubernetes.io/zone: region-a
      containers:
      - name: forwarder
        env:
        - name: REGION
          value: "region-a"

---
# 区域B部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kvm-tunnel-forwarder-region-b
  namespace: kvm-tunnel
spec:
  replicas: 2
  template:
    spec:
      nodeSelector:
        topology.kubernetes.io/zone: region-b
      containers:
      - name: forwarder
        env:
        - name: REGION
          value: "region-b"
```

#### 15.4.2 Pod反亲和性配置

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kvm-tunnel-forwarder
spec:
  template:
    spec:
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - kvm-tunnel-forwarder
              topologyKey: kubernetes.io/hostname
          - weight: 50
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - kvm-tunnel-forwarder
              topologyKey: topology.kubernetes.io/zone
```

### 15.5 自动扩缩容

#### 15.5.1 HPA配置

```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: kvm-tunnel-forwarder-hpa
  namespace: kvm-tunnel
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: kvm-tunnel-forwarder
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  - type: Pods
    pods:
      metric:
        name: active_sessions
      target:
        type: AverageValue
        averageValue: "100"
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max
```

#### 15.5.2 VPA配置

```yaml
# vpa.yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: kvm-tunnel-forwarder-vpa
  namespace: kvm-tunnel
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: kvm-tunnel-forwarder
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: forwarder
      minAllowed:
        cpu: 100m
        memory: 128Mi
      maxAllowed:
        cpu: 4
        memory: 8Gi
      controlledResources: ["cpu", "memory"]
```

### 15.6 网络配置

#### 15.6.1 NetworkPolicy

```yaml
# network-policy.yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: kvm-tunnel-forwarder-netpol
  namespace: kvm-tunnel
spec:
  podSelector:
    matchLabels:
      app: kvm-tunnel-forwarder
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # 允许来自管理服务器的连接
  - from:
    - namespaceSelector:
        matchLabels:
          name: kvm-management
    ports:
    - protocol: TCP
      port: 8443
  # 允许节点间通信
  - from:
    - podSelector:
        matchLabels:
          app: kvm-tunnel-forwarder
    ports:
    - protocol: TCP
      port: 8443
  # 允许健康检查
  - from: []
    ports:
    - protocol: TCP
      port: 8080
  egress:
  # 允许访问Redis
  - to:
    - namespaceSelector:
        matchLabels:
          name: redis
    ports:
    - protocol: TCP
      port: 6379
  # 允许访问etcd
  - to:
    - namespaceSelector:
        matchLabels:
          name: etcd
    ports:
    - protocol: TCP
      port: 2379
  # 允许DNS解析
  - to: []
    ports:
    - protocol: UDP
      port: 53
```

### 15.7 监控和日志集成

#### 15.7.1 ServiceMonitor配置

```yaml
# servicemonitor.yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: kvm-tunnel-forwarder
  namespace: kvm-tunnel
  labels:
    app: kvm-tunnel-forwarder
spec:
  selector:
    matchLabels:
      app: kvm-tunnel-forwarder
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
    honorLabels: true
```

#### 15.7.2 日志收集配置

```yaml
# fluentd-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-kvm-tunnel-config
  namespace: kvm-tunnel
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/kvm_tunnel/*.log
      pos_file /var/log/fluentd-kvm-tunnel.log.pos
      tag kvm_tunnel.*
      format json
      time_key timestamp
      time_format %Y-%m-%dT%H:%M:%S.%NZ
    </source>

    <filter kvm_tunnel.**>
      @type kubernetes_metadata
      @id filter_kube_metadata
    </filter>

    <match kvm_tunnel.**>
      @type elasticsearch
      host elasticsearch.logging.svc.cluster.local
      port 9200
      index_name kvm-tunnel
      type_name _doc
    </match>
```

## 16. 测试策略

### 16.1 测试金字塔

转发节点采用分层测试策略，确保代码质量和系统可靠性：

```mermaid
graph TB
    subgraph "测试金字塔"
        E2E[端到端测试<br/>E2E Tests<br/>10%]
        INT[集成测试<br/>Integration Tests<br/>20%]
        UNIT[单元测试<br/>Unit Tests<br/>70%]
    end

    subgraph "测试类型"
        FUNC[功能测试]
        PERF[性能测试]
        SEC[安全测试]
        CHAOS[混沌测试]
    end

    E2E --> FUNC
    INT --> PERF
    UNIT --> SEC
    INT --> CHAOS

    style E2E fill:#ff5722
    style INT fill:#ff9800
    style UNIT fill:#4caf50
```

### 16.2 单元测试策略

#### 16.2.1 测试覆盖率要求

- **代码覆盖率**：≥ 85%
- **分支覆盖率**：≥ 80%
- **函数覆盖率**：≥ 90%

#### 16.2.2 单元测试框架

```rust
// tests/unit/session_manager_test.rs
use kvm_tunnel::core::SessionManager;
use kvm_tunnel::types::{SessionConfig, SessionType, SessionStatus};
use tokio_test;
use mockall::predicate::*;

#[cfg(test)]
mod session_manager_tests {
    use super::*;

    #[tokio::test]
    async fn test_create_transmitter_session_success() {
        // Arrange
        let manager = SessionManager::new();
        let config = SessionConfig {
            session_type: SessionType::Transmitter,
            device_id: "test_device".to_string(),
            transports: vec![],
        };

        // Act
        let result = manager.create_session(config).await;

        // Assert
        assert!(result.is_ok());
        let session_id = result.unwrap();
        assert!(!session_id.is_empty());

        let session_info = manager.get_session(&session_id).await.unwrap();
        assert_eq!(session_info.status, SessionStatus::Active);
    }

    #[tokio::test]
    async fn test_create_session_with_invalid_config() {
        let manager = SessionManager::new();
        let config = SessionConfig {
            session_type: SessionType::Transmitter,
            device_id: "".to_string(), // 无效的设备ID
            transports: vec![],
        };

        let result = manager.create_session(config).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_session_lifecycle() {
        let manager = SessionManager::new();
        let config = create_valid_config();

        // 创建会话
        let session_id = manager.create_session(config).await.unwrap();

        // 验证会话存在
        assert!(manager.session_exists(&session_id).await);

        // 销毁会话
        manager.destroy_session(&session_id).await.unwrap();

        // 验证会话已删除
        assert!(!manager.session_exists(&session_id).await);
    }

    #[tokio::test]
    async fn test_concurrent_session_creation() {
        let manager = Arc::new(SessionManager::new());
        let mut handles = vec![];

        // 并发创建100个会话
        for i in 0..100 {
            let manager_clone = manager.clone();
            let handle = tokio::spawn(async move {
                let config = create_test_config(i);
                manager_clone.create_session(config).await
            });
            handles.push(handle);
        }

        // 等待所有任务完成
        let results: Vec<_> = futures::future::join_all(handles).await;

        // 验证所有会话都创建成功
        let successful_sessions: Vec<_> = results
            .into_iter()
            .filter_map(|r| r.ok().and_then(|r| r.ok()))
            .collect();

        assert_eq!(successful_sessions.len(), 100);
    }
}

// Mock对象测试
#[cfg(test)]
mod mock_tests {
    use super::*;
    use mockall::mock;

    mock! {
        TransportManager {}

        #[async_trait]
        impl TransportManager for TransportManager {
            async fn create_transport(&self, config: TransportConfig) -> Result<TransportId, TransportError>;
            async fn destroy_transport(&self, id: &TransportId) -> Result<(), TransportError>;
        }
    }

    #[tokio::test]
    async fn test_session_with_mock_transport() {
        let mut mock_transport = MockTransportManager::new();
        mock_transport
            .expect_create_transport()
            .times(1)
            .returning(|_| Ok("transport_123".to_string()));

        let manager = SessionManager::with_transport_manager(Box::new(mock_transport));
        let config = create_valid_config();

        let result = manager.create_session(config).await;
        assert!(result.is_ok());
    }
}
```

### 16.3 集成测试策略

#### 16.3.1 测试环境搭建

```rust
// tests/integration/test_environment.rs
use testcontainers::{clients::Cli, images::redis::Redis, Container};
use kvm_tunnel::ForwarderNode;

pub struct TestEnvironment {
    pub redis_container: Container<'static, Redis>,
    pub forwarder_nodes: Vec<ForwarderNode>,
    pub docker: Cli,
}

impl TestEnvironment {
    pub async fn new() -> Self {
        let docker = Cli::default();

        // 启动Redis容器
        let redis_container = docker.run(Redis::default());
        let redis_port = redis_container.get_host_port_ipv4(6379);

        // 配置转发节点
        let config = ForwarderConfig {
            redis: RedisConfig {
                url: format!("redis://localhost:{}", redis_port),
                ..Default::default()
            },
            ..Default::default()
        };

        Self {
            redis_container,
            forwarder_nodes: vec![],
            docker,
        }
    }

    pub async fn start_forwarder_node(&mut self, node_id: &str) -> Result<(), TestError> {
        let config = self.create_node_config(node_id);
        let node = ForwarderNode::new(config).await?;
        node.start().await?;
        self.forwarder_nodes.push(node);
        Ok(())
    }

    pub async fn cleanup(&mut self) {
        for node in &mut self.forwarder_nodes {
            let _ = node.stop().await;
        }
        self.forwarder_nodes.clear();
    }
}

// 集成测试用例
#[tokio::test]
async fn test_multi_node_communication() {
    let mut env = TestEnvironment::new().await;

    // 启动两个节点
    env.start_forwarder_node("node1").await.unwrap();
    env.start_forwarder_node("node2").await.unwrap();

    // 在node2上创建TX会话
    let tx_config = create_transmitter_config();
    let tx_session = env.forwarder_nodes[1]
        .create_transmitter_session(tx_config)
        .await
        .unwrap();

    // 在node1上创建RX会话，连接到node2的TX会话
    let rx_config = create_receiver_config(&tx_session, "node2");
    let rx_session = env.forwarder_nodes[0]
        .create_receiver_session(rx_config)
        .await
        .unwrap();

    // 等待连接建立
    tokio::time::sleep(Duration::from_millis(100)).await;

    // 验证数据传输
    let tx_stats = env.forwarder_nodes[1].get_session_stats(&tx_session).await.unwrap();
    let rx_stats = env.forwarder_nodes[0].get_session_stats(&rx_session).await.unwrap();

    assert!(tx_stats.bytes_sent > 0);
    assert!(rx_stats.bytes_received > 0);
    assert_eq!(tx_stats.bytes_sent, rx_stats.bytes_received);

    env.cleanup().await;
}
```

### 16.4 性能测试策略

#### 16.4.1 基准测试

```rust
// benches/performance_benchmarks.rs
use criterion::{criterion_group, criterion_main, Criterion, BenchmarkId};
use kvm_tunnel::*;
use std::time::Duration;

fn benchmark_session_operations(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();
    let manager = rt.block_on(async { SessionManager::new() });

    let mut group = c.benchmark_group("session_operations");

    // 测试不同并发级别下的会话创建性能
    for concurrency in [1, 10, 50, 100].iter() {
        group.bench_with_input(
            BenchmarkId::new("create_session", concurrency),
            concurrency,
            |b, &concurrency| {
                b.iter(|| {
                    rt.block_on(async {
                        let handles: Vec<_> = (0..concurrency)
                            .map(|i| {
                                let manager = manager.clone();
                                tokio::spawn(async move {
                                    let config = create_test_config(i);
                                    manager.create_session(config).await
                                })
                            })
                            .collect();

                        futures::future::join_all(handles).await
                    })
                })
            },
        );
    }

    group.finish();
}

fn benchmark_data_forwarding(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();
    let setup = rt.block_on(async { setup_forwarding_environment().await });

    let mut group = c.benchmark_group("data_forwarding");

    // 测试不同数据包大小的转发性能
    for packet_size in [1024, 4096, 16384, 65536].iter() {
        group.bench_with_input(
            BenchmarkId::new("forward_packet", packet_size),
            packet_size,
            |b, &packet_size| {
                b.iter(|| {
                    rt.block_on(async {
                        let data = vec![0u8; packet_size];
                        setup.forward_data(data).await
                    })
                })
            },
        );
    }

    group.finish();
}

criterion_group!(benches, benchmark_session_operations, benchmark_data_forwarding);
criterion_main!(benches);
```

#### 16.4.2 负载测试

```rust
// tests/load/load_test.rs
use kvm_tunnel::*;
use tokio::time::{Duration, Instant};
use std::sync::Arc;
use std::sync::atomic::{AtomicU64, Ordering};

#[tokio::test]
async fn test_high_concurrency_sessions() {
    let manager = Arc::new(SessionManager::new());
    let success_count = Arc::new(AtomicU64::new(0));
    let error_count = Arc::new(AtomicU64::new(0));

    let start_time = Instant::now();
    let mut handles = vec![];

    // 并发创建1000个会话
    for i in 0..1000 {
        let manager = manager.clone();
        let success_count = success_count.clone();
        let error_count = error_count.clone();

        let handle = tokio::spawn(async move {
            let config = create_load_test_config(i);
            match manager.create_session(config).await {
                Ok(_) => success_count.fetch_add(1, Ordering::Relaxed),
                Err(_) => error_count.fetch_add(1, Ordering::Relaxed),
            };
        });

        handles.push(handle);
    }

    // 等待所有任务完成
    futures::future::join_all(handles).await;

    let duration = start_time.elapsed();
    let success = success_count.load(Ordering::Relaxed);
    let errors = error_count.load(Ordering::Relaxed);

    println!("Load test results:");
    println!("  Duration: {:?}", duration);
    println!("  Successful sessions: {}", success);
    println!("  Failed sessions: {}", errors);
    println!("  Success rate: {:.2}%", (success as f64 / 1000.0) * 100.0);
    println!("  Throughput: {:.2} sessions/sec", success as f64 / duration.as_secs_f64());

    // 验证性能指标
    assert!(success >= 950, "Success rate should be at least 95%");
    assert!(duration.as_secs() < 10, "Should complete within 10 seconds");
}

#[tokio::test]
async fn test_sustained_load() {
    let manager = Arc::new(SessionManager::new());
    let metrics = Arc::new(LoadTestMetrics::new());

    // 持续5分钟的负载测试
    let test_duration = Duration::from_secs(300);
    let start_time = Instant::now();

    while start_time.elapsed() < test_duration {
        let batch_start = Instant::now();
        let mut batch_handles = vec![];

        // 每秒创建50个会话
        for i in 0..50 {
            let manager = manager.clone();
            let metrics = metrics.clone();

            let handle = tokio::spawn(async move {
                let session_start = Instant::now();
                let config = create_sustained_test_config(i);

                match manager.create_session(config).await {
                    Ok(session_id) => {
                        let creation_time = session_start.elapsed();
                        metrics.record_success(creation_time);

                        // 保持会话活跃一段时间后销毁
                        tokio::time::sleep(Duration::from_secs(30)).await;
                        let _ = manager.destroy_session(&session_id).await;
                    }
                    Err(e) => {
                        metrics.record_error(e);
                    }
                }
            });

            batch_handles.push(handle);
        }

        // 等待当前批次完成或超时
        let batch_timeout = Duration::from_millis(900);
        let _ = tokio::time::timeout(batch_timeout, futures::future::join_all(batch_handles)).await;

        // 控制请求频率
        let batch_duration = batch_start.elapsed();
        if batch_duration < Duration::from_secs(1) {
            tokio::time::sleep(Duration::from_secs(1) - batch_duration).await;
        }
    }

    let final_metrics = metrics.get_summary();
    println!("Sustained load test results: {:#?}", final_metrics);

    // 验证系统稳定性
    assert!(final_metrics.success_rate >= 0.95, "Success rate should remain above 95%");
    assert!(final_metrics.avg_response_time.as_millis() < 100, "Average response time should be under 100ms");
}
```

### 16.5 端到端测试

#### 16.5.1 E2E测试框架

```rust
// tests/e2e/full_system_test.rs
use kvm_tunnel::*;
use testcontainers::*;
use std::process::Command;

pub struct E2ETestEnvironment {
    management_server: Container<'static, GenericImage>,
    forwarder_nodes: Vec<Container<'static, GenericImage>>,
    redis_cluster: Container<'static, GenericImage>,
    etcd_cluster: Container<'static, GenericImage>,
}

impl E2ETestEnvironment {
    pub async fn setup() -> Self {
        let docker = Cli::default();

        // 启动基础设施
        let redis_cluster = docker.run(
            GenericImage::new("redis", "7-alpine")
                .with_exposed_port(6379)
        );

        let etcd_cluster = docker.run(
            GenericImage::new("quay.io/coreos/etcd", "v3.5.0")
                .with_exposed_port(2379)
                .with_env_var("ETCD_ADVERTISE_CLIENT_URLS", "http://0.0.0.0:2379")
                .with_env_var("ETCD_LISTEN_CLIENT_URLS", "http://0.0.0.0:2379")
        );

        // 启动管理服务器
        let management_server = docker.run(
            GenericImage::new("kvm-tunnel/management-server", "latest")
                .with_exposed_port(8080)
        );

        // 启动转发节点
        let mut forwarder_nodes = vec![];
        for i in 1..=3 {
            let node = docker.run(
                GenericImage::new("kvm-tunnel/forwarder", "latest")
                    .with_exposed_port(8080)
                    .with_exposed_port(8443)
                    .with_env_var("NODE_ID", format!("forwarder-{}", i))
                    .with_env_var("MANAGEMENT_SERVER_URL",
                        format!("http://localhost:{}", management_server.get_host_port_ipv4(8080)))
            );
            forwarder_nodes.push(node);
        }

        Self {
            management_server,
            forwarder_nodes,
            redis_cluster,
            etcd_cluster,
        }
    }
}

#[tokio::test]
async fn test_complete_kvm_forwarding_flow() {
    let env = E2ETestEnvironment::setup().await;

    // 等待所有服务启动
    tokio::time::sleep(Duration::from_secs(10)).await;

    // 模拟KVM TX设备连接到节点1
    let tx_client = create_mock_kvm_tx_client();
    let tx_session_response = tx_client
        .connect_to_forwarder(&env.forwarder_nodes[0])
        .await
        .expect("TX device should connect successfully");

    // 模拟KVM RX设备请求访问TX设备（通过管理服务器）
    let rx_client = create_mock_kvm_rx_client();
    let access_request = AccessRequest {
        target_device_id: tx_session_response.device_id,
        requesting_device_id: "rx_device_001".to_string(),
    };

    let management_client = ManagementClient::new(
        format!("http://localhost:{}", env.management_server.get_host_port_ipv4(8080))
    );

    let access_response = management_client
        .request_access(access_request)
        .await
        .expect("Access request should succeed");

    // RX设备连接到分配的转发节点
    let rx_session_response = rx_client
        .connect_with_access_token(&access_response)
        .await
        .expect("RX device should connect successfully");

    // 验证端到端连接
    let test_data = b"Hello, KVM!";
    tx_client.send_data(test_data).await.expect("Should send data");

    let received_data = rx_client
        .receive_data_timeout(Duration::from_secs(5))
        .await
        .expect("Should receive data within timeout");

    assert_eq!(test_data, received_data.as_slice());

    // 验证统计信息
    let tx_stats = tx_client.get_statistics().await.unwrap();
    let rx_stats = rx_client.get_statistics().await.unwrap();

    assert!(tx_stats.bytes_sent > 0);
    assert!(rx_stats.bytes_received > 0);
    assert_eq!(tx_stats.bytes_sent, rx_stats.bytes_received);

    // 清理连接
    tx_client.disconnect().await.unwrap();
    rx_client.disconnect().await.unwrap();
}
```

### 16.6 安全测试

#### 16.6.1 认证授权测试

```rust
// tests/security/auth_test.rs
use kvm_tunnel::security::*;
use openssl::x509::X509;

#[tokio::test]
async fn test_mtls_authentication() {
    let security_manager = SecurityManager::new(create_test_security_config());

    // 测试有效证书
    let valid_cert = load_test_certificate("valid_management_server.pem");
    let result = security_manager
        .verify_management_call(&valid_cert, "/api/v1/sessions")
        .await;
    assert!(result.is_ok());

    // 测试过期证书
    let expired_cert = load_test_certificate("expired_cert.pem");
    let result = security_manager
        .verify_management_call(&expired_cert, "/api/v1/sessions")
        .await;
    assert!(matches!(result, Err(SecurityError::CertificateExpired)));

    // 测试不受信任的证书
    let untrusted_cert = load_test_certificate("untrusted_cert.pem");
    let result = security_manager
        .verify_management_call(&untrusted_cert, "/api/v1/sessions")
        .await;
    assert!(matches!(result, Err(SecurityError::CertificateInvalid)));
}

#[tokio::test]
async fn test_api_authorization() {
    let security_manager = SecurityManager::new(create_test_security_config());
    let management_cert = load_test_certificate("valid_management_server.pem");
    let node_cert = load_test_certificate("valid_forwarder_node.pem");

    // 管理服务器调用管理API - 应该成功
    let result = security_manager
        .verify_management_call(&management_cert, "/api/v1/sessions/transmitter")
        .await;
    assert!(result.is_ok());

    // 转发节点调用管理API - 应该失败
    let result = security_manager
        .verify_node_call(&node_cert, "node-001", "/api/v1/sessions/transmitter")
        .await;
    assert!(matches!(result, Err(SecurityError::UnauthorizedEndpoint)));

    // 转发节点调用节点间API - 应该成功
    let result = security_manager
        .verify_node_call(&node_cert, "node-001", "/api/v1/inter-node/access-request")
        .await;
    assert!(result.is_ok());
}

#[tokio::test]
async fn test_rate_limiting() {
    let security_manager = SecurityManager::new(create_test_security_config());
    let node_cert = load_test_certificate("valid_forwarder_node.pem");

    // 快速连续调用，触发速率限制
    for i in 0..100 {
        let result = security_manager
            .verify_node_call(&node_cert, "node-001", "/api/v1/inter-node/status")
            .await;

        if i < 50 {
            assert!(result.is_ok(), "First 50 requests should succeed");
        } else {
            // 后续请求应该被速率限制
            if result.is_err() {
                assert!(matches!(result, Err(SecurityError::RateLimited)));
                break;
            }
        }
    }
}
```

### 16.7 混沌测试

#### 16.7.1 网络故障模拟

```rust
// tests/chaos/network_chaos_test.rs
use kvm_tunnel::*;
use chaos_engineering::*;

#[tokio::test]
async fn test_network_partition_resilience() {
    let mut cluster = create_test_cluster(3).await;

    // 建立正常的跨节点会话
    let tx_session = cluster.nodes[0].create_transmitter_session(create_test_config()).await.unwrap();
    let rx_session = cluster.nodes[2].create_receiver_session(
        create_receiver_config(&tx_session, &cluster.nodes[0].node_id)
    ).await.unwrap();

    // 验证初始连接正常
    assert!(verify_data_flow(&cluster.nodes[0], &cluster.nodes[2]).await);

    // 模拟网络分区：隔离中间节点
    let chaos_controller = ChaosController::new();
    chaos_controller.isolate_node(&cluster.nodes[1]).await;

    // 验证直连节点间的通信仍然正常
    tokio::time::sleep(Duration::from_secs(5)).await;
    assert!(verify_data_flow(&cluster.nodes[0], &cluster.nodes[2]).await);

    // 恢复网络连接
    chaos_controller.restore_node(&cluster.nodes[1]).await;

    // 验证集群完全恢复
    tokio::time::sleep(Duration::from_secs(10)).await;
    assert!(cluster.verify_cluster_health().await);
}

#[tokio::test]
async fn test_random_node_failures() {
    let mut cluster = create_test_cluster(5).await;
    let chaos_controller = ChaosController::new();

    // 创建多个会话分布在不同节点上
    let mut sessions = vec![];
    for i in 0..10 {
        let tx_node = &cluster.nodes[i % 5];
        let rx_node = &cluster.nodes[(i + 2) % 5];

        let tx_session = tx_node.create_transmitter_session(create_test_config()).await.unwrap();
        let rx_session = rx_node.create_receiver_session(
            create_receiver_config(&tx_session, &tx_node.node_id)
        ).await.unwrap();

        sessions.push((tx_session, rx_session, i % 5, (i + 2) % 5));
    }

    // 随机故障注入
    for _ in 0..20 {
        // 随机选择一个节点进行故障注入
        let target_node = rand::random::<usize>() % 5;
        let failure_type = rand::random::<u8>() % 3;

        match failure_type {
            0 => {
                // CPU负载过高
                chaos_controller.inject_cpu_stress(&cluster.nodes[target_node], 0.9).await;
                tokio::time::sleep(Duration::from_secs(30)).await;
                chaos_controller.stop_cpu_stress(&cluster.nodes[target_node]).await;
            }
            1 => {
                // 内存压力
                chaos_controller.inject_memory_pressure(&cluster.nodes[target_node], 0.8).await;
                tokio::time::sleep(Duration::from_secs(30)).await;
                chaos_controller.stop_memory_pressure(&cluster.nodes[target_node]).await;
            }
            2 => {
                // 网络延迟
                chaos_controller.inject_network_delay(&cluster.nodes[target_node], Duration::from_millis(500)).await;
                tokio::time::sleep(Duration::from_secs(30)).await;
                chaos_controller.stop_network_delay(&cluster.nodes[target_node]).await;
            }
            _ => unreachable!(),
        }

        // 验证系统恢复能力
        tokio::time::sleep(Duration::from_secs(10)).await;
        let healthy_sessions = count_healthy_sessions(&cluster, &sessions).await;

        // 至少80%的会话应该保持健康
        assert!(healthy_sessions >= 8, "At least 80% of sessions should remain healthy during chaos");
    }
}
```

### 16.8 自动化测试流水线

#### 16.8.1 CI/CD配置

```yaml
# .github/workflows/test.yml
name: Test Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Install Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable
        components: rustfmt, clippy

    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: |
          ~/.cargo/registry
          ~/.cargo/git
          target
        key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}

    - name: Run unit tests
      run: |
        cargo test --lib --bins
        cargo test --doc

    - name: Generate coverage report
      run: |
        cargo install cargo-tarpaulin
        cargo tarpaulin --out xml --output-dir coverage

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: coverage/cobertura.xml

  integration-tests:
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
      etcd:
        image: quay.io/coreos/etcd:v3.5.0
        ports:
          - 2379:2379
        env:
          ETCD_ADVERTISE_CLIENT_URLS: http://localhost:2379
          ETCD_LISTEN_CLIENT_URLS: http://0.0.0.0:2379

    steps:
    - uses: actions/checkout@v3

    - name: Install Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable

    - name: Run integration tests
      run: cargo test --test integration
      env:
        REDIS_URL: redis://localhost:6379
        ETCD_ENDPOINTS: http://localhost:2379

  performance-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Install Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable

    - name: Run benchmark tests
      run: |
        cargo install cargo-criterion
        cargo criterion --output-format json > benchmark_results.json

    - name: Upload benchmark results
      uses: actions/upload-artifact@v3
      with:
        name: benchmark-results
        path: benchmark_results.json

  security-tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Install Rust
      uses: actions-rs/toolchain@v1
      with:
        toolchain: stable

    - name: Security audit
      run: |
        cargo install cargo-audit
        cargo audit

    - name: Run security tests
      run: cargo test --test security

  e2e-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'push'

    steps:
    - uses: actions/checkout@v3

    - name: Build Docker images
      run: |
        docker build -t kvm-tunnel/forwarder:test .
        docker build -t kvm-tunnel/management-server:test ./management-server

    - name: Run E2E tests
      run: |
        docker-compose -f docker-compose.test.yml up -d
        cargo test --test e2e
        docker-compose -f docker-compose.test.yml down
```

## 17. 运维监控

### 17.1 监控体系架构

转发节点采用分层监控架构，提供全方位的可观测性：

```mermaid
graph TB
    subgraph "数据采集层"
        APP[应用指标<br/>Prometheus]
        SYS[系统指标<br/>Node Exporter]
        LOG[日志数据<br/>Fluentd]
        TRACE[链路追踪<br/>Jaeger]
    end

    subgraph "数据存储层"
        PROM[Prometheus<br/>指标存储]
        ES[Elasticsearch<br/>日志存储]
        JAEGER[Jaeger<br/>追踪存储]
    end

    subgraph "可视化层"
        GRAF[Grafana<br/>监控面板]
        KIBANA[Kibana<br/>日志分析]
        JAEGER_UI[Jaeger UI<br/>链路分析]
    end

    subgraph "告警层"
        ALERT[AlertManager<br/>告警管理]
        WEBHOOK[Webhook<br/>通知集成]
    end

    APP --> PROM
    SYS --> PROM
    LOG --> ES
    TRACE --> JAEGER

    PROM --> GRAF
    PROM --> ALERT
    ES --> KIBANA
    JAEGER --> JAEGER_UI

    ALERT --> WEBHOOK

    style APP fill:#4caf50
    style SYS fill:#2196f3
    style LOG fill:#ff9800
    style TRACE fill:#9c27b0
    style PROM fill:#e91e63
    style GRAF fill:#00bcd4
    style ALERT fill:#f44336
```

### 17.2 核心监控指标

#### 17.2.1 业务指标

```rust
// src/monitoring/business_metrics.rs
use prometheus::{Counter, Gauge, Histogram, IntGauge, Registry};

#[derive(Clone)]
pub struct BusinessMetrics {
    // 会话相关指标
    pub sessions_total: Counter,
    pub sessions_active: IntGauge,
    pub sessions_failed: Counter,
    pub session_duration: Histogram,

    // 数据传输指标
    pub bytes_transferred_total: Counter,
    pub packets_transferred_total: Counter,
    pub packet_loss_rate: Gauge,
    pub transfer_latency: Histogram,

    // 节点间通信指标
    pub inter_node_requests_total: Counter,
    pub inter_node_request_duration: Histogram,
    pub inter_node_connections_active: IntGauge,

    // 错误指标
    pub errors_total: Counter,
    pub certificate_errors_total: Counter,
    pub timeout_errors_total: Counter,
}

impl BusinessMetrics {
    pub fn new(registry: &Registry) -> Result<Self, prometheus::Error> {
        let sessions_total = Counter::new(
            "kvm_tunnel_sessions_total",
            "Total number of sessions created"
        )?;

        let sessions_active = IntGauge::new(
            "kvm_tunnel_sessions_active",
            "Number of currently active sessions"
        )?;

        let sessions_failed = Counter::new(
            "kvm_tunnel_sessions_failed_total",
            "Total number of failed session attempts"
        )?;

        let session_duration = Histogram::with_opts(
            prometheus::HistogramOpts::new(
                "kvm_tunnel_session_duration_seconds",
                "Duration of sessions in seconds"
            ).buckets(vec![1.0, 5.0, 10.0, 30.0, 60.0, 300.0, 600.0, 1800.0, 3600.0])
        )?;

        let bytes_transferred_total = Counter::new(
            "kvm_tunnel_bytes_transferred_total",
            "Total bytes transferred through the tunnel"
        )?;

        let packets_transferred_total = Counter::new(
            "kvm_tunnel_packets_transferred_total",
            "Total packets transferred through the tunnel"
        )?;

        let packet_loss_rate = Gauge::new(
            "kvm_tunnel_packet_loss_rate",
            "Current packet loss rate (0.0 to 1.0)"
        )?;

        let transfer_latency = Histogram::with_opts(
            prometheus::HistogramOpts::new(
                "kvm_tunnel_transfer_latency_seconds",
                "Latency of data transfer operations"
            ).buckets(prometheus::exponential_buckets(0.001, 2.0, 15)?)
        )?;

        // 注册所有指标
        registry.register(Box::new(sessions_total.clone()))?;
        registry.register(Box::new(sessions_active.clone()))?;
        registry.register(Box::new(sessions_failed.clone()))?;
        registry.register(Box::new(session_duration.clone()))?;
        registry.register(Box::new(bytes_transferred_total.clone()))?;
        registry.register(Box::new(packets_transferred_total.clone()))?;
        registry.register(Box::new(packet_loss_rate.clone()))?;
        registry.register(Box::new(transfer_latency.clone()))?;

        Ok(Self {
            sessions_total,
            sessions_active,
            sessions_failed,
            session_duration,
            bytes_transferred_total,
            packets_transferred_total,
            packet_loss_rate,
            transfer_latency,
            // ... 其他指标
        })
    }

    pub fn record_session_created(&self, session_type: &str) {
        self.sessions_total.with_label_values(&[session_type]).inc();
        self.sessions_active.inc();
    }

    pub fn record_session_ended(&self, session_type: &str, duration: Duration) {
        self.sessions_active.dec();
        self.session_duration.with_label_values(&[session_type]).observe(duration.as_secs_f64());
    }

    pub fn record_data_transfer(&self, bytes: u64, packets: u64, latency: Duration) {
        self.bytes_transferred_total.inc_by(bytes);
        self.packets_transferred_total.inc_by(packets);
        self.transfer_latency.observe(latency.as_secs_f64());
    }
}
```

#### 17.2.2 系统指标

```rust
// src/monitoring/system_metrics.rs
use prometheus::{Gauge, IntGauge, Registry};
use sysinfo::{System, SystemExt, CpuExt, DiskExt, NetworkExt};

#[derive(Clone)]
pub struct SystemMetrics {
    pub cpu_usage: Gauge,
    pub memory_usage: Gauge,
    pub memory_total: IntGauge,
    pub memory_available: IntGauge,
    pub disk_usage: Gauge,
    pub disk_total: IntGauge,
    pub network_rx_bytes: IntGauge,
    pub network_tx_bytes: IntGauge,
    pub open_file_descriptors: IntGauge,
    pub goroutines: IntGauge,
}

impl SystemMetrics {
    pub fn new(registry: &Registry) -> Result<Self, prometheus::Error> {
        let cpu_usage = Gauge::new(
            "kvm_tunnel_cpu_usage_percent",
            "CPU usage percentage"
        )?;

        let memory_usage = Gauge::new(
            "kvm_tunnel_memory_usage_percent",
            "Memory usage percentage"
        )?;

        let memory_total = IntGauge::new(
            "kvm_tunnel_memory_total_bytes",
            "Total memory in bytes"
        )?;

        let memory_available = IntGauge::new(
            "kvm_tunnel_memory_available_bytes",
            "Available memory in bytes"
        )?;

        // 注册指标
        registry.register(Box::new(cpu_usage.clone()))?;
        registry.register(Box::new(memory_usage.clone()))?;
        registry.register(Box::new(memory_total.clone()))?;
        registry.register(Box::new(memory_available.clone()))?;

        Ok(Self {
            cpu_usage,
            memory_usage,
            memory_total,
            memory_available,
            // ... 其他指标
        })
    }

    pub fn update_system_metrics(&self) {
        let mut system = System::new_all();
        system.refresh_all();

        // CPU使用率
        let cpu_usage = system.global_cpu_info().cpu_usage();
        self.cpu_usage.set(cpu_usage as f64);

        // 内存使用情况
        let total_memory = system.total_memory();
        let available_memory = system.available_memory();
        let used_memory = total_memory - available_memory;
        let memory_usage_percent = (used_memory as f64 / total_memory as f64) * 100.0;

        self.memory_total.set(total_memory as i64);
        self.memory_available.set(available_memory as i64);
        self.memory_usage.set(memory_usage_percent);

        // 网络统计
        for (interface_name, network) in system.networks() {
            if interface_name == "eth0" || interface_name == "en0" {
                self.network_rx_bytes.set(network.total_received() as i64);
                self.network_tx_bytes.set(network.total_transmitted() as i64);
                break;
            }
        }
    }
}

// 定期更新系统指标
pub async fn start_system_metrics_collector(metrics: SystemMetrics) {
    let mut interval = tokio::time::interval(Duration::from_secs(15));

    loop {
        interval.tick().await;
        metrics.update_system_metrics();
    }
}
```

### 17.3 告警策略

#### 17.3.1 告警规则配置

```yaml
# prometheus/alert_rules.yml
groups:
- name: kvm_tunnel_alerts
  rules:
  # 高可用性告警
  - alert: ForwarderNodeDown
    expr: up{job="kvm-tunnel-forwarder"} == 0
    for: 1m
    labels:
      severity: critical
      service: kvm-tunnel
    annotations:
      summary: "转发节点 {{ $labels.instance }} 离线"
      description: "转发节点 {{ $labels.instance }} 已经离线超过1分钟"
      runbook_url: "https://docs.kvm-tunnel.com/runbooks/node-down"

  - alert: HighSessionFailureRate
    expr: rate(kvm_tunnel_sessions_failed_total[5m]) / rate(kvm_tunnel_sessions_total[5m]) > 0.1
    for: 2m
    labels:
      severity: warning
      service: kvm-tunnel
    annotations:
      summary: "会话失败率过高"
      description: "节点 {{ $labels.instance }} 的会话失败率在过去5分钟内超过10%"

  # 性能告警
  - alert: HighCPUUsage
    expr: kvm_tunnel_cpu_usage_percent > 80
    for: 5m
    labels:
      severity: warning
      service: kvm-tunnel
    annotations:
      summary: "CPU使用率过高"
      description: "节点 {{ $labels.instance }} CPU使用率超过80%，当前值: {{ $value }}%"

  - alert: HighMemoryUsage
    expr: kvm_tunnel_memory_usage_percent > 85
    for: 3m
    labels:
      severity: warning
      service: kvm-tunnel
    annotations:
      summary: "内存使用率过高"
      description: "节点 {{ $labels.instance }} 内存使用率超过85%，当前值: {{ $value }}%"

  - alert: HighPacketLossRate
    expr: kvm_tunnel_packet_loss_rate > 0.05
    for: 1m
    labels:
      severity: critical
      service: kvm-tunnel
    annotations:
      summary: "数据包丢失率过高"
      description: "节点 {{ $labels.instance }} 数据包丢失率超过5%，当前值: {{ $value }}"

  # 安全告警
  - alert: HighCertificateErrorRate
    expr: rate(kvm_tunnel_certificate_errors_total[5m]) > 0.1
    for: 1m
    labels:
      severity: warning
      service: kvm-tunnel
    annotations:
      summary: "证书错误率过高"
      description: "节点 {{ $labels.instance }} 证书验证错误率过高，可能存在安全问题"

  - alert: UnauthorizedAccessAttempts
    expr: rate(kvm_tunnel_unauthorized_access_total[5m]) > 0.5
    for: 1m
    labels:
      severity: critical
      service: kvm-tunnel
    annotations:
      summary: "检测到未授权访问尝试"
      description: "节点 {{ $labels.instance }} 检测到频繁的未授权访问尝试"

  # 容量告警
  - alert: TooManyActiveSessions
    expr: kvm_tunnel_sessions_active > 900
    for: 2m
    labels:
      severity: warning
      service: kvm-tunnel
    annotations:
      summary: "活跃会话数量接近上限"
      description: "节点 {{ $labels.instance }} 活跃会话数量: {{ $value }}，接近1000的上限"

  - alert: HighNetworkBandwidthUsage
    expr: rate(kvm_tunnel_bytes_transferred_total[5m]) * 8 / 1024 / 1024 > 800
    for: 3m
    labels:
      severity: warning
      service: kvm-tunnel
    annotations:
      summary: "网络带宽使用率过高"
      description: "节点 {{ $labels.instance }} 网络带宽使用率超过800Mbps"
```

#### 17.3.2 告警管理器配置

```yaml
# alertmanager/alertmanager.yml
global:
  smtp_smarthost: 'smtp.company.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'password'

route:
  group_by: ['alertname', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default'
  routes:
  - match:
      severity: critical
    receiver: 'critical-alerts'
    group_wait: 5s
    repeat_interval: 30m
  - match:
      severity: warning
    receiver: 'warning-alerts'
    repeat_interval: 2h

receivers:
- name: 'default'
  email_configs:
  - to: '<EMAIL>'
    subject: '[KVM Tunnel] {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      Instance: {{ .Labels.instance }}
      Severity: {{ .Labels.severity }}
      {{ end }}

- name: 'critical-alerts'
  email_configs:
  - to: '<EMAIL>,<EMAIL>'
    subject: '[CRITICAL] KVM Tunnel Alert: {{ .GroupLabels.alertname }}'
    body: |
      🚨 CRITICAL ALERT 🚨

      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      Instance: {{ .Labels.instance }}
      Time: {{ .StartsAt }}
      Runbook: {{ .Annotations.runbook_url }}
      {{ end }}

  slack_configs:
  - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
    channel: '#ops-critical'
    title: 'KVM Tunnel Critical Alert'
    text: |
      {{ range .Alerts }}
      🚨 *{{ .Annotations.summary }}*

      *Description:* {{ .Annotations.description }}
      *Instance:* {{ .Labels.instance }}
      *Severity:* {{ .Labels.severity }}
      {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
      {{ end }}

- name: 'warning-alerts'
  email_configs:
  - to: '<EMAIL>'
    subject: '[WARNING] KVM Tunnel Alert: {{ .GroupLabels.alertname }}'

  slack_configs:
  - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
    channel: '#ops-warnings'
    title: 'KVM Tunnel Warning'

inhibit_rules:
- source_match:
    severity: 'critical'
  target_match:
    severity: 'warning'
  equal: ['alertname', 'instance']
```

### 17.4 监控面板

#### 17.4.1 Grafana仪表板配置

```json
{
  "dashboard": {
    "id": null,
    "title": "KVM Tunnel - 转发节点监控",
    "tags": ["kvm-tunnel", "forwarder"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "节点状态概览",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"kvm-tunnel-forwarder\"}",
            "legendFormat": "{{instance}}"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {
              "mode": "thresholds"
            },
            "thresholds": {
              "steps": [
                {"color": "red", "value": 0},
                {"color": "green", "value": 1}
              ]
            },
            "mappings": [
              {"options": {"0": {"text": "离线"}}, "type": "value"},
              {"options": {"1": {"text": "在线"}}, "type": "value"}
            ]
          }
        },
        "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "活跃会话数",
        "type": "stat",
        "targets": [
          {
            "expr": "sum(kvm_tunnel_sessions_active)",
            "legendFormat": "总活跃会话"
          }
        ],
        "fieldConfig": {
          "defaults": {
            "color": {"mode": "palette-classic"},
            "unit": "short"
          }
        },
        "gridPos": {"h": 4, "w": 6, "x": 6, "y": 0}
      },
      {
        "id": 3,
        "title": "数据传输速率",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(kvm_tunnel_bytes_transferred_total[5m]) * 8 / 1024 / 1024",
            "legendFormat": "{{instance}} - 传输速率 (Mbps)"
          }
        ],
        "yAxes": [
          {
            "label": "Mbps",
            "min": 0
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 4}
      },
      {
        "id": 4,
        "title": "系统资源使用率",
        "type": "graph",
        "targets": [
          {
            "expr": "kvm_tunnel_cpu_usage_percent",
            "legendFormat": "{{instance}} - CPU %"
          },
          {
            "expr": "kvm_tunnel_memory_usage_percent",
            "legendFormat": "{{instance}} - Memory %"
          }
        ],
        "yAxes": [
          {
            "label": "百分比",
            "min": 0,
            "max": 100
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 4}
      },
      {
        "id": 5,
        "title": "会话创建趋势",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(kvm_tunnel_sessions_total[5m])",
            "legendFormat": "{{instance}} - 会话创建率"
          },
          {
            "expr": "rate(kvm_tunnel_sessions_failed_total[5m])",
            "legendFormat": "{{instance}} - 会话失败率"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}
      },
      {
        "id": 6,
        "title": "网络延迟分布",
        "type": "heatmap",
        "targets": [
          {
            "expr": "rate(kvm_tunnel_transfer_latency_seconds_bucket[5m])",
            "legendFormat": "{{le}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 12}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

#### 17.4.2 自定义监控面板

```rust
// src/monitoring/dashboard.rs
use serde_json::json;

pub struct DashboardGenerator {
    panels: Vec<Panel>,
}

impl DashboardGenerator {
    pub fn new() -> Self {
        Self {
            panels: vec![],
        }
    }

    pub fn add_business_metrics_panel(&mut self) {
        let panel = Panel {
            id: 1,
            title: "业务指标概览".to_string(),
            panel_type: PanelType::Stat,
            targets: vec![
                Target {
                    expr: "sum(kvm_tunnel_sessions_active)".to_string(),
                    legend: "活跃会话".to_string(),
                },
                Target {
                    expr: "rate(kvm_tunnel_sessions_total[5m])".to_string(),
                    legend: "会话创建率".to_string(),
                },
            ],
            grid_pos: GridPos { h: 4, w: 12, x: 0, y: 0 },
        };
        self.panels.push(panel);
    }

    pub fn add_performance_panel(&mut self) {
        let panel = Panel {
            id: 2,
            title: "性能指标".to_string(),
            panel_type: PanelType::Graph,
            targets: vec![
                Target {
                    expr: "histogram_quantile(0.95, rate(kvm_tunnel_transfer_latency_seconds_bucket[5m]))".to_string(),
                    legend: "P95延迟".to_string(),
                },
                Target {
                    expr: "histogram_quantile(0.99, rate(kvm_tunnel_transfer_latency_seconds_bucket[5m]))".to_string(),
                    legend: "P99延迟".to_string(),
                },
            ],
            grid_pos: GridPos { h: 8, w: 12, x: 0, y: 4 },
        };
        self.panels.push(panel);
    }

    pub fn generate_dashboard(&self) -> serde_json::Value {
        json!({
            "dashboard": {
                "title": "KVM Tunnel - 转发节点监控",
                "panels": self.panels,
                "time": {
                    "from": "now-1h",
                    "to": "now"
                },
                "refresh": "30s"
            }
        })
    }
}
```

### 17.5 日志管理

#### 17.5.1 结构化日志配置

```rust
// src/logging/structured_logging.rs
use tracing::{info, warn, error, debug};
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use serde_json::json;

pub fn init_structured_logging() -> Result<(), Box<dyn std::error::Error>> {
    let formatting_layer = tracing_subscriber::fmt::layer()
        .json()
        .with_current_span(false)
        .with_span_list(true);

    let filter_layer = tracing_subscriber::EnvFilter::try_from_default_env()
        .unwrap_or_else(|_| "kvm_tunnel=info,tower_http=debug".into());

    tracing_subscriber::registry()
        .with(filter_layer)
        .with(formatting_layer)
        .init();

    Ok(())
}

// 业务事件日志记录
pub struct BusinessEventLogger;

impl BusinessEventLogger {
    pub fn log_session_created(session_id: &str, session_type: &str, device_id: &str) {
        info!(
            event = "session_created",
            session_id = session_id,
            session_type = session_type,
            device_id = device_id,
            "New session created successfully"
        );
    }

    pub fn log_session_failed(device_id: &str, error: &str, reason: &str) {
        warn!(
            event = "session_failed",
            device_id = device_id,
            error = error,
            reason = reason,
            "Session creation failed"
        );
    }

    pub fn log_inter_node_communication(source_node: &str, target_node: &str, action: &str, success: bool) {
        if success {
            info!(
                event = "inter_node_communication",
                source_node = source_node,
                target_node = target_node,
                action = action,
                success = success,
                "Inter-node communication successful"
            );
        } else {
            error!(
                event = "inter_node_communication",
                source_node = source_node,
                target_node = target_node,
                action = action,
                success = success,
                "Inter-node communication failed"
            );
        }
    }

    pub fn log_security_event(event_type: &str, source_ip: &str, certificate_fingerprint: Option<&str>, result: &str) {
        warn!(
            event = "security_event",
            event_type = event_type,
            source_ip = source_ip,
            certificate_fingerprint = certificate_fingerprint,
            result = result,
            "Security event detected"
        );
    }
}
```

#### 17.5.2 日志聚合配置

```yaml
# fluentd/fluent.conf
<source>
  @type tail
  path /var/log/kvm_tunnel/*.log
  pos_file /var/log/fluentd-kvm-tunnel.log.pos
  tag kvm_tunnel.raw
  format json
  time_key timestamp
  time_format %Y-%m-%dT%H:%M:%S.%NZ
</source>

# 解析和丰富日志
<filter kvm_tunnel.raw>
  @type parser
  key_name message
  reserve_data true
  <parse>
    @type json
  </parse>
</filter>

<filter kvm_tunnel.raw>
  @type record_transformer
  <record>
    service kvm_tunnel
    environment ${ENV}
    node_id ${NODE_ID}
    region ${REGION}
  </record>
</filter>

# 根据日志级别路由
<match kvm_tunnel.raw>
  @type rewrite_tag_filter
  <rule>
    key level
    pattern ^ERROR$
    tag kvm_tunnel.error
  </rule>
  <rule>
    key level
    pattern ^WARN$
    tag kvm_tunnel.warning
  </rule>
  <rule>
    key level
    pattern ^INFO$
    tag kvm_tunnel.info
  </rule>
  <rule>
    key level
    pattern .*
    tag kvm_tunnel.debug
  </rule>
</match>

# 错误日志特殊处理
<filter kvm_tunnel.error>
  @type record_transformer
  <record>
    alert_required true
    severity critical
  </record>
</filter>

# 输出到Elasticsearch
<match kvm_tunnel.**>
  @type elasticsearch
  host elasticsearch.logging.svc.cluster.local
  port 9200
  index_name kvm-tunnel-${tag_parts[1]}-%Y.%m.%d
  type_name _doc

  <buffer tag,time>
    @type file
    path /var/log/fluentd-buffers/kvm-tunnel
    timekey 1h
    timekey_wait 10m
    flush_mode interval
    flush_interval 30s
    chunk_limit_size 32MB
    queue_limit_length 128
  </buffer>
</match>

# 错误日志同时发送告警
<match kvm_tunnel.error>
  @type copy
  <store>
    @type elasticsearch
    host elasticsearch.logging.svc.cluster.local
    port 9200
    index_name kvm-tunnel-errors-%Y.%m.%d
  </store>
  <store>
    @type webhook
    endpoint http://alertmanager:9093/api/v1/alerts
    http_method post
    serializer json
    <format>
      @type json
    </format>
    <buffer>
      flush_interval 10s
    </buffer>
  </store>
</match>
```

### 17.6 链路追踪

#### 17.6.1 分布式追踪配置

```rust
// src/tracing/distributed_tracing.rs
use opentelemetry::{global, sdk::trace as sdktrace, trace::TraceError};
use opentelemetry_jaeger as jaeger;
use tracing_opentelemetry::OpenTelemetryLayer;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

pub fn init_tracing() -> Result<(), TraceError> {
    let tracer = jaeger::new_agent_pipeline()
        .with_service_name("kvm-tunnel-forwarder")
        .with_tags(vec![
            ("version", env!("CARGO_PKG_VERSION")),
            ("environment", std::env::var("ENVIRONMENT").unwrap_or_else(|_| "development".to_string())),
        ])
        .install_simple()?;

    let telemetry_layer = OpenTelemetryLayer::new(tracer);

    tracing_subscriber::registry()
        .with(telemetry_layer)
        .with(tracing_subscriber::fmt::layer())
        .init();

    Ok(())
}

// 业务操作追踪
#[tracing::instrument(skip(self))]
pub async fn create_session(&self, config: SessionConfig) -> Result<SessionId, SessionError> {
    let span = tracing::Span::current();
    span.record("session_type", &config.session_type.to_string());
    span.record("device_id", &config.device_id);

    // 创建会话逻辑
    let session_id = self.do_create_session(config).await?;

    span.record("session_id", &session_id);
    span.record("result", "success");

    Ok(session_id)
}

#[tracing::instrument(skip(self))]
pub async fn inter_node_request(&self, target_node: &str, request: InterNodeRequest) -> Result<InterNodeResponse, InterNodeError> {
    let span = tracing::Span::current();
    span.record("target_node", target_node);
    span.record("request_type", &request.request_type());

    let start_time = std::time::Instant::now();

    let result = self.send_request(target_node, request).await;

    let duration = start_time.elapsed();
    span.record("duration_ms", duration.as_millis() as i64);

    match &result {
        Ok(_) => span.record("result", "success"),
        Err(e) => {
            span.record("result", "error");
            span.record("error", &e.to_string());
        }
    }

    result
}
```

## 18. 性能调优

### 18.1 性能基准

#### 18.1.1 基准测试环境

**硬件配置**：
- CPU: Intel Xeon E5-2686 v4 (8 cores, 2.3GHz)
- 内存: 16GB DDR4
- 网络: 10Gbps Ethernet
- 存储: NVMe SSD

**软件环境**：
- OS: Ubuntu 22.04 LTS
- Rust: 1.70+
- Docker: 20.10+
- Kubernetes: 1.25+

#### 18.1.2 性能指标基准

| 指标 | 目标值 | 优秀值 | 测试条件 |
|------|--------|--------|----------|
| 会话创建延迟 | < 100ms | < 50ms | 单节点，无负载 |
| 数据转发延迟 | < 10ms | < 5ms | 1KB数据包 |
| 最大并发会话 | 1000 | 2000 | 8核16GB配置 |
| 吞吐量 | 1Gbps | 5Gbps | 单节点转发 |
| CPU使用率 | < 70% | < 50% | 500并发会话 |
| 内存使用率 | < 80% | < 60% | 1000并发会话 |
| 包丢失率 | < 0.1% | < 0.01% | 正常网络条件 |

### 18.2 系统调优

#### 18.2.1 操作系统调优

```bash
#!/bin/bash
# system_tuning.sh - 系统性能调优脚本

# 网络参数优化
echo "优化网络参数..."
cat >> /etc/sysctl.conf << EOF
# 网络缓冲区大小
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216

# TCP缓冲区大小
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216

# 连接队列大小
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000

# TCP连接优化
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_syn_backlog = 8192
net.ipv4.tcp_max_tw_buckets = 5000

# 文件描述符限制
fs.file-max = 2097152
fs.nr_open = 2097152
EOF

# 应用系统参数
sysctl -p

# 用户限制优化
echo "优化用户限制..."
cat >> /etc/security/limits.conf << EOF
* soft nofile 1048576
* hard nofile 1048576
* soft nproc 1048576
* hard nproc 1048576
EOF

# CPU调度优化
echo "优化CPU调度..."
echo 'GRUB_CMDLINE_LINUX_DEFAULT="quiet splash isolcpus=1-3 rcu_nocbs=1-3"' >> /etc/default/grub
update-grub

# 内存优化
echo "优化内存管理..."
echo 'vm.swappiness = 1' >> /etc/sysctl.conf
echo 'vm.dirty_ratio = 15' >> /etc/sysctl.conf
echo 'vm.dirty_background_ratio = 5' >> /etc/sysctl.conf

echo "系统调优完成，建议重启系统使所有设置生效"
```

#### 18.2.2 容器运行时优化

```yaml
# deployment-optimized.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kvm-tunnel-forwarder-optimized
spec:
  template:
    spec:
      containers:
      - name: forwarder
        image: kvm-tunnel/forwarder:optimized
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "8Gi"
            cpu: "4000m"
        env:
        # Rust运行时优化
        - name: RUST_LOG
          value: "info"
        - name: RUST_BACKTRACE
          value: "0"  # 生产环境关闭backtrace

        # Tokio运行时优化
        - name: TOKIO_WORKER_THREADS
          value: "8"  # 设置工作线程数
        - name: TOKIO_BLOCKING_THREADS
          value: "16"  # 设置阻塞线程池大小

        # 应用特定优化
        - name: MAX_SESSIONS_PER_NODE
          value: "2000"
        - name: SESSION_CLEANUP_INTERVAL
          value: "60"
        - name: METRICS_COLLECTION_INTERVAL
          value: "30"

        securityContext:
          capabilities:
            add:
            - NET_ADMIN  # 网络管理权限
            - SYS_NICE   # 进程优先级调整

        # CPU亲和性设置
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
              - matchExpressions:
                - key: node-type
                  operator: In
                  values: ["high-performance"]
```

### 18.3 应用层调优

#### 18.3.1 Rust编译优化

```toml
# Cargo.toml - 生产环境优化配置
[profile.release]
# 启用最高级别优化
opt-level = 3
# 启用链接时优化
lto = true
# 减少代码大小
codegen-units = 1
# 启用panic=abort以减少二进制大小
panic = "abort"
# 启用调试信息用于性能分析
debug = true

[profile.release.package."*"]
# 对所有依赖启用优化
opt-level = 3

# 特定依赖优化
[dependencies]
tokio = { version = "1.0", features = ["full", "tracing"] }
# 使用jemalloc作为内存分配器
jemallocator = "0.5"

# 编译时特性优化
[features]
default = ["optimized"]
optimized = ["simd", "native-tls"]
simd = []  # SIMD指令优化
native-tls = []  # 原生TLS实现
```

```rust
// src/main.rs - 运行时优化配置
#[cfg(feature = "optimized")]
#[global_allocator]
static ALLOC: jemallocator::Jemalloc = jemallocator::Jemalloc;

#[tokio::main(flavor = "multi_thread", worker_threads = 8)]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 设置线程优先级
    #[cfg(unix)]
    unsafe {
        libc::setpriority(libc::PRIO_PROCESS, 0, -10);
    }

    // 预分配内存池
    let _memory_pool = MemoryPool::with_capacity(1024 * 1024 * 100); // 100MB

    // 启动应用
    let app = ForwarderNode::new(load_config()?).await?;
    app.run().await
}
```

#### 18.3.2 网络层优化

```rust
// src/network/optimized_transport.rs
use tokio::net::{TcpSocket, UdpSocket};
use socket2::{Socket, Domain, Type, Protocol};

pub struct OptimizedNetworkConfig {
    pub tcp_nodelay: bool,
    pub tcp_keepalive: Option<Duration>,
    pub socket_recv_buffer_size: Option<usize>,
    pub socket_send_buffer_size: Option<usize>,
    pub reuse_port: bool,
}

impl Default for OptimizedNetworkConfig {
    fn default() -> Self {
        Self {
            tcp_nodelay: true,
            tcp_keepalive: Some(Duration::from_secs(60)),
            socket_recv_buffer_size: Some(2 * 1024 * 1024), // 2MB
            socket_send_buffer_size: Some(2 * 1024 * 1024), // 2MB
            reuse_port: true,
        }
    }
}

pub async fn create_optimized_tcp_listener(
    addr: SocketAddr,
    config: &OptimizedNetworkConfig,
) -> Result<TcpListener, std::io::Error> {
    let socket = Socket::new(Domain::IPV4, Type::STREAM, Some(Protocol::TCP))?;

    // 启用地址重用
    socket.set_reuse_address(true)?;

    if config.reuse_port {
        socket.set_reuse_port(true)?;
    }

    // 设置缓冲区大小
    if let Some(size) = config.socket_recv_buffer_size {
        socket.set_recv_buffer_size(size)?;
    }

    if let Some(size) = config.socket_send_buffer_size {
        socket.set_send_buffer_size(size)?;
    }

    // 绑定和监听
    socket.bind(&addr.into())?;
    socket.listen(1024)?; // 增大监听队列

    let std_listener: std::net::TcpListener = socket.into();
    std_listener.set_nonblocking(true)?;

    TcpListener::from_std(std_listener)
}

// 零拷贝数据传输
pub struct ZeroCopyBuffer {
    inner: Vec<u8>,
    capacity: usize,
}

impl ZeroCopyBuffer {
    pub fn with_capacity(capacity: usize) -> Self {
        Self {
            inner: Vec::with_capacity(capacity),
            capacity,
        }
    }

    pub async fn send_vectored<W>(&self, writer: &mut W, data: &[&[u8]]) -> Result<usize, std::io::Error>
    where
        W: AsyncWrite + Unpin,
    {
        // 使用vectored I/O减少系统调用
        let io_slices: Vec<IoSlice> = data.iter().map(|&buf| IoSlice::new(buf)).collect();
        writer.write_vectored(&io_slices).await
    }
}
```

### 18.4 内存优化

#### 18.4.1 内存池设计

```rust
// src/memory/pool.rs
use std::sync::Arc;
use std::collections::VecDeque;
use tokio::sync::Mutex;

pub struct MemoryPool<T> {
    pool: Arc<Mutex<VecDeque<T>>>,
    factory: Box<dyn Fn() -> T + Send + Sync>,
    max_size: usize,
}

impl<T> MemoryPool<T>
where
    T: Send + 'static,
{
    pub fn new<F>(factory: F, max_size: usize) -> Self
    where
        F: Fn() -> T + Send + Sync + 'static,
    {
        Self {
            pool: Arc::new(Mutex::new(VecDeque::with_capacity(max_size))),
            factory: Box::new(factory),
            max_size,
        }
    }

    pub async fn acquire(&self) -> PooledObject<T> {
        let mut pool = self.pool.lock().await;
        let object = pool.pop_front().unwrap_or_else(|| (self.factory)());

        PooledObject {
            object: Some(object),
            pool: self.pool.clone(),
            max_size: self.max_size,
        }
    }
}

pub struct PooledObject<T> {
    object: Option<T>,
    pool: Arc<Mutex<VecDeque<T>>>,
    max_size: usize,
}

impl<T> std::ops::Deref for PooledObject<T> {
    type Target = T;

    fn deref(&self) -> &Self::Target {
        self.object.as_ref().unwrap()
    }
}

impl<T> std::ops::DerefMut for PooledObject<T> {
    fn deref_mut(&mut self) -> &mut Self::Target {
        self.object.as_mut().unwrap()
    }
}

impl<T> Drop for PooledObject<T> {
    fn drop(&mut self) {
        if let Some(object) = self.object.take() {
            let pool = self.pool.clone();
            let max_size = self.max_size;

            tokio::spawn(async move {
                let mut pool = pool.lock().await;
                if pool.len() < max_size {
                    pool.push_back(object);
                }
            });
        }
    }
}

// 缓冲区池
pub type BufferPool = MemoryPool<Vec<u8>>;

impl BufferPool {
    pub fn new_buffer_pool(buffer_size: usize, pool_size: usize) -> Self {
        Self::new(
            move || Vec::with_capacity(buffer_size),
            pool_size,
        )
    }
}

// 全局缓冲区池
lazy_static::lazy_static! {
    pub static ref GLOBAL_BUFFER_POOL: BufferPool = BufferPool::new_buffer_pool(64 * 1024, 1000);
}
```

#### 18.4.2 内存使用监控

```rust
// src/memory/monitor.rs
use std::sync::atomic::{AtomicUsize, Ordering};
use std::time::{Duration, Instant};

pub struct MemoryMonitor {
    allocated_bytes: AtomicUsize,
    peak_usage: AtomicUsize,
    allocation_count: AtomicUsize,
    last_gc_time: Arc<Mutex<Instant>>,
}

impl MemoryMonitor {
    pub fn new() -> Self {
        Self {
            allocated_bytes: AtomicUsize::new(0),
            peak_usage: AtomicUsize::new(0),
            allocation_count: AtomicUsize::new(0),
            last_gc_time: Arc::new(Mutex::new(Instant::now())),
        }
    }

    pub fn record_allocation(&self, size: usize) {
        let current = self.allocated_bytes.fetch_add(size, Ordering::Relaxed) + size;
        self.allocation_count.fetch_add(1, Ordering::Relaxed);

        // 更新峰值使用量
        let mut peak = self.peak_usage.load(Ordering::Relaxed);
        while current > peak {
            match self.peak_usage.compare_exchange_weak(
                peak,
                current,
                Ordering::Relaxed,
                Ordering::Relaxed,
            ) {
                Ok(_) => break,
                Err(x) => peak = x,
            }
        }
    }

    pub fn record_deallocation(&self, size: usize) {
        self.allocated_bytes.fetch_sub(size, Ordering::Relaxed);
    }

    pub fn get_memory_stats(&self) -> MemoryStats {
        MemoryStats {
            current_usage: self.allocated_bytes.load(Ordering::Relaxed),
            peak_usage: self.peak_usage.load(Ordering::Relaxed),
            allocation_count: self.allocation_count.load(Ordering::Relaxed),
        }
    }

    pub async fn trigger_gc_if_needed(&self) {
        let current_usage = self.allocated_bytes.load(Ordering::Relaxed);
        let peak_usage = self.peak_usage.load(Ordering::Relaxed);

        // 如果当前使用量超过峰值的80%，触发垃圾回收
        if current_usage > peak_usage * 8 / 10 {
            let mut last_gc = self.last_gc_time.lock().await;
            let now = Instant::now();

            // 限制GC频率，最多每30秒一次
            if now.duration_since(*last_gc) > Duration::from_secs(30) {
                self.force_gc().await;
                *last_gc = now;
            }
        }
    }

    async fn force_gc(&self) {
        // 清理过期的会话和连接
        // 释放未使用的缓冲区
        // 压缩内存池

        tracing::info!("Triggered garbage collection");
    }
}

#[derive(Debug, Clone)]
pub struct MemoryStats {
    pub current_usage: usize,
    pub peak_usage: usize,
    pub allocation_count: usize,
}
```

### 18.5 并发优化

#### 18.5.1 异步任务调度优化

```rust
// src/concurrency/scheduler.rs
use tokio::task::{JoinHandle, LocalSet};
use std::sync::Arc;
use std::collections::HashMap;

pub struct TaskScheduler {
    cpu_bound_pool: Arc<rayon::ThreadPool>,
    io_bound_executor: tokio::runtime::Handle,
    local_sets: HashMap<usize, LocalSet>,
}

impl TaskScheduler {
    pub fn new() -> Self {
        let cpu_cores = num_cpus::get();

        // CPU密集型任务线程池
        let cpu_bound_pool = Arc::new(
            rayon::ThreadPoolBuilder::new()
                .num_threads(cpu_cores)
                .thread_name(|i| format!("cpu-worker-{}", i))
                .build()
                .expect("Failed to create CPU thread pool")
        );

        // 为每个CPU核心创建本地任务集
        let mut local_sets = HashMap::new();
        for i in 0..cpu_cores {
            local_sets.insert(i, LocalSet::new());
        }

        Self {
            cpu_bound_pool,
            io_bound_executor: tokio::runtime::Handle::current(),
            local_sets,
        }
    }

    // CPU密集型任务调度
    pub async fn spawn_cpu_task<F, R>(&self, task: F) -> Result<R, tokio::task::JoinError>
    where
        F: FnOnce() -> R + Send + 'static,
        R: Send + 'static,
    {
        let pool = self.cpu_bound_pool.clone();

        tokio::task::spawn_blocking(move || {
            pool.install(|| task())
        }).await
    }

    // I/O密集型任务调度
    pub fn spawn_io_task<F>(&self, task: F) -> JoinHandle<F::Output>
    where
        F: Future + Send + 'static,
        F::Output: Send + 'static,
    {
        self.io_bound_executor.spawn(task)
    }

    // 本地任务调度（避免跨线程开销）
    pub async fn spawn_local_task<F>(&self, core_id: usize, task: F) -> Result<F::Output, tokio::task::JoinError>
    where
        F: Future + 'static,
        F::Output: 'static,
    {
        if let Some(local_set) = self.local_sets.get(&core_id) {
            local_set.spawn_local(task).await
        } else {
            // 回退到普通任务调度
            tokio::task::spawn_local(task).await
        }
    }
}

// 工作负载均衡器
pub struct WorkloadBalancer {
    workers: Vec<WorkerHandle>,
    current_worker: AtomicUsize,
    load_metrics: Arc<Mutex<HashMap<usize, WorkerLoad>>>,
}

impl WorkloadBalancer {
    pub fn new(worker_count: usize) -> Self {
        let mut workers = Vec::with_capacity(worker_count);
        let load_metrics = Arc::new(Mutex::new(HashMap::new()));

        for i in 0..worker_count {
            let (tx, rx) = mpsc::unbounded_channel();
            let worker = WorkerHandle {
                id: i,
                sender: tx,
                load: Arc::new(AtomicUsize::new(0)),
            };

            // 启动工作线程
            let worker_load = worker.load.clone();
            let load_metrics_clone = load_metrics.clone();

            tokio::spawn(async move {
                Self::worker_loop(i, rx, worker_load, load_metrics_clone).await;
            });

            workers.push(worker);
        }

        Self {
            workers,
            current_worker: AtomicUsize::new(0),
            load_metrics,
        }
    }

    // 基于负载的任务分发
    pub async fn dispatch_task<T>(&self, task: T) -> Result<(), SendError<T>>
    where
        T: Send + 'static,
    {
        // 选择负载最低的工作线程
        let worker_id = self.select_least_loaded_worker().await;
        let worker = &self.workers[worker_id];

        worker.sender.send(task)?;
        worker.load.fetch_add(1, Ordering::Relaxed);

        Ok(())
    }

    async fn select_least_loaded_worker(&self) -> usize {
        let load_metrics = self.load_metrics.lock().await;

        self.workers
            .iter()
            .enumerate()
            .min_by_key(|(_, worker)| worker.load.load(Ordering::Relaxed))
            .map(|(id, _)| id)
            .unwrap_or(0)
    }

    async fn worker_loop<T>(
        worker_id: usize,
        mut receiver: mpsc::UnboundedReceiver<T>,
        load_counter: Arc<AtomicUsize>,
        load_metrics: Arc<Mutex<HashMap<usize, WorkerLoad>>>,
    ) {
        while let Some(task) = receiver.recv().await {
            let start_time = Instant::now();

            // 处理任务
            // process_task(task).await;

            let duration = start_time.elapsed();
            load_counter.fetch_sub(1, Ordering::Relaxed);

            // 更新负载指标
            let mut metrics = load_metrics.lock().await;
            let worker_load = metrics.entry(worker_id).or_insert_with(WorkerLoad::new);
            worker_load.update(duration);
        }
    }
}

#[derive(Debug)]
struct WorkerHandle {
    id: usize,
    sender: mpsc::UnboundedSender<Box<dyn Send>>,
    load: Arc<AtomicUsize>,
}

#[derive(Debug)]
struct WorkerLoad {
    avg_processing_time: Duration,
    task_count: usize,
}

impl WorkerLoad {
    fn new() -> Self {
        Self {
            avg_processing_time: Duration::from_millis(0),
            task_count: 0,
        }
    }

    fn update(&mut self, processing_time: Duration) {
        self.task_count += 1;

        // 计算移动平均值
        let alpha = 0.1; // 平滑因子
        let new_avg = self.avg_processing_time.as_nanos() as f64 * (1.0 - alpha) +
                      processing_time.as_nanos() as f64 * alpha;

        self.avg_processing_time = Duration::from_nanos(new_avg as u64);
    }
}
```

### 18.6 容量规划

#### 18.6.1 容量评估模型

```rust
// src/capacity/planning.rs
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CapacityModel {
    pub hardware_specs: HardwareSpecs,
    pub workload_profile: WorkloadProfile,
    pub performance_targets: PerformanceTargets,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HardwareSpecs {
    pub cpu_cores: usize,
    pub memory_gb: usize,
    pub network_bandwidth_gbps: f64,
    pub storage_iops: usize,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkloadProfile {
    pub avg_session_duration_minutes: f64,
    pub peak_concurrent_sessions: usize,
    pub avg_data_rate_mbps: f64,
    pub session_creation_rate_per_second: f64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceTargets {
    pub max_session_creation_latency_ms: f64,
    pub max_data_forwarding_latency_ms: f64,
    pub min_throughput_mbps: f64,
    pub max_cpu_utilization_percent: f64,
    pub max_memory_utilization_percent: f64,
}

impl CapacityModel {
    pub fn calculate_capacity(&self) -> CapacityEstimate {
        let cpu_capacity = self.estimate_cpu_capacity();
        let memory_capacity = self.estimate_memory_capacity();
        let network_capacity = self.estimate_network_capacity();

        CapacityEstimate {
            max_concurrent_sessions: cpu_capacity.min(memory_capacity).min(network_capacity),
            cpu_utilization_at_max: self.calculate_cpu_utilization_at_capacity(cpu_capacity),
            memory_utilization_at_max: self.calculate_memory_utilization_at_capacity(memory_capacity),
            network_utilization_at_max: self.calculate_network_utilization_at_capacity(network_capacity),
            bottleneck: self.identify_bottleneck(cpu_capacity, memory_capacity, network_capacity),
        }
    }

    fn estimate_cpu_capacity(&self) -> usize {
        // 基于CPU核心数和目标CPU使用率估算容量
        let cpu_per_session = 0.01; // 每个会话平均占用1%的CPU
        let available_cpu = self.hardware_specs.cpu_cores as f64 *
                           (self.performance_targets.max_cpu_utilization_percent / 100.0);

        (available_cpu / cpu_per_session) as usize
    }

    fn estimate_memory_capacity(&self) -> usize {
        // 基于内存大小估算容量
        let memory_per_session_mb = 2.0; // 每个会话平均占用2MB内存
        let available_memory_mb = self.hardware_specs.memory_gb as f64 * 1024.0 *
                                 (self.performance_targets.max_memory_utilization_percent / 100.0);

        (available_memory_mb / memory_per_session_mb) as usize
    }

    fn estimate_network_capacity(&self) -> usize {
        // 基于网络带宽估算容量
        let available_bandwidth_mbps = self.hardware_specs.network_bandwidth_gbps * 1000.0;
        let required_bandwidth_per_session = self.workload_profile.avg_data_rate_mbps;

        (available_bandwidth_mbps / required_bandwidth_per_session) as usize
    }

    fn identify_bottleneck(&self, cpu_cap: usize, mem_cap: usize, net_cap: usize) -> Bottleneck {
        let min_capacity = cpu_cap.min(mem_cap).min(net_cap);

        if min_capacity == cpu_cap {
            Bottleneck::CPU
        } else if min_capacity == mem_cap {
            Bottleneck::Memory
        } else {
            Bottleneck::Network
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CapacityEstimate {
    pub max_concurrent_sessions: usize,
    pub cpu_utilization_at_max: f64,
    pub memory_utilization_at_max: f64,
    pub network_utilization_at_max: f64,
    pub bottleneck: Bottleneck,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Bottleneck {
    CPU,
    Memory,
    Network,
    Storage,
}

// 容量规划工具
pub struct CapacityPlanner;

impl CapacityPlanner {
    pub fn generate_scaling_recommendations(
        current_model: &CapacityModel,
        target_sessions: usize,
    ) -> ScalingRecommendations {
        let current_capacity = current_model.calculate_capacity();

        if target_sessions <= current_capacity.max_concurrent_sessions {
            return ScalingRecommendations {
                scaling_required: false,
                recommendations: vec![],
            };
        }

        let scale_factor = target_sessions as f64 / current_capacity.max_concurrent_sessions as f64;
        let mut recommendations = vec![];

        match current_capacity.bottleneck {
            Bottleneck::CPU => {
                let required_cores = (current_model.hardware_specs.cpu_cores as f64 * scale_factor).ceil() as usize;
                recommendations.push(ScalingRecommendation {
                    resource_type: ResourceType::CPU,
                    current_value: current_model.hardware_specs.cpu_cores,
                    recommended_value: required_cores,
                    reason: "CPU是当前瓶颈，需要增加CPU核心数".to_string(),
                });
            }
            Bottleneck::Memory => {
                let required_memory = (current_model.hardware_specs.memory_gb as f64 * scale_factor).ceil() as usize;
                recommendations.push(ScalingRecommendation {
                    resource_type: ResourceType::Memory,
                    current_value: current_model.hardware_specs.memory_gb,
                    recommended_value: required_memory,
                    reason: "内存是当前瓶颈，需要增加内存容量".to_string(),
                });
            }
            Bottleneck::Network => {
                let required_bandwidth = current_model.hardware_specs.network_bandwidth_gbps * scale_factor;
                recommendations.push(ScalingRecommendation {
                    resource_type: ResourceType::Network,
                    current_value: current_model.hardware_specs.network_bandwidth_gbps as usize,
                    recommended_value: required_bandwidth.ceil() as usize,
                    reason: "网络带宽是当前瓶颈，需要升级网络".to_string(),
                });
            }
            Bottleneck::Storage => {
                // 存储瓶颈处理逻辑
            }
        }

        ScalingRecommendations {
            scaling_required: true,
            recommendations,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingRecommendations {
    pub scaling_required: bool,
    pub recommendations: Vec<ScalingRecommendation>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ScalingRecommendation {
    pub resource_type: ResourceType,
    pub current_value: usize,
    pub recommended_value: usize,
    pub reason: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ResourceType {
    CPU,
    Memory,
    Network,
    Storage,
}
```

### 18.7 性能监控和分析

#### 18.7.1 性能分析工具

```rust
// src/profiling/performance_analyzer.rs
use std::time::{Duration, Instant};
use std::collections::HashMap;
use tokio::sync::RwLock;

pub struct PerformanceAnalyzer {
    metrics: Arc<RwLock<HashMap<String, PerformanceMetric>>>,
    sampling_rate: f64,
}

impl PerformanceAnalyzer {
    pub fn new(sampling_rate: f64) -> Self {
        Self {
            metrics: Arc::new(RwLock::new(HashMap::new())),
            sampling_rate,
        }
    }

    pub async fn start_measurement(&self, operation: &str) -> Option<MeasurementHandle> {
        if rand::random::<f64>() > self.sampling_rate {
            return None;
        }

        Some(MeasurementHandle {
            operation: operation.to_string(),
            start_time: Instant::now(),
            analyzer: self.metrics.clone(),
        })
    }

    pub async fn get_performance_report(&self) -> PerformanceReport {
        let metrics = self.metrics.read().await;
        let mut operations = Vec::new();

        for (name, metric) in metrics.iter() {
            operations.push(OperationStats {
                name: name.clone(),
                count: metric.count,
                avg_duration: metric.total_duration / metric.count as u32,
                min_duration: metric.min_duration,
                max_duration: metric.max_duration,
                p95_duration: metric.calculate_percentile(0.95),
                p99_duration: metric.calculate_percentile(0.99),
            });
        }

        PerformanceReport { operations }
    }

    async fn record_measurement(&self, operation: String, duration: Duration) {
        let mut metrics = self.metrics.write().await;
        let metric = metrics.entry(operation).or_insert_with(PerformanceMetric::new);
        metric.record(duration);
    }
}

pub struct MeasurementHandle {
    operation: String,
    start_time: Instant,
    analyzer: Arc<RwLock<HashMap<String, PerformanceMetric>>>,
}

impl Drop for MeasurementHandle {
    fn drop(&mut self) {
        let duration = self.start_time.elapsed();
        let operation = self.operation.clone();
        let analyzer = self.analyzer.clone();

        tokio::spawn(async move {
            let mut metrics = analyzer.write().await;
            let metric = metrics.entry(operation).or_insert_with(PerformanceMetric::new);
            metric.record(duration);
        });
    }
}

#[derive(Debug)]
struct PerformanceMetric {
    count: usize,
    total_duration: Duration,
    min_duration: Duration,
    max_duration: Duration,
    durations: Vec<Duration>, // 用于计算百分位数
}

impl PerformanceMetric {
    fn new() -> Self {
        Self {
            count: 0,
            total_duration: Duration::from_nanos(0),
            min_duration: Duration::from_secs(u64::MAX),
            max_duration: Duration::from_nanos(0),
            durations: Vec::new(),
        }
    }

    fn record(&mut self, duration: Duration) {
        self.count += 1;
        self.total_duration += duration;
        self.min_duration = self.min_duration.min(duration);
        self.max_duration = self.max_duration.max(duration);

        // 保留最近1000个样本用于百分位数计算
        if self.durations.len() >= 1000 {
            self.durations.remove(0);
        }
        self.durations.push(duration);
    }

    fn calculate_percentile(&self, percentile: f64) -> Duration {
        if self.durations.is_empty() {
            return Duration::from_nanos(0);
        }

        let mut sorted_durations = self.durations.clone();
        sorted_durations.sort();

        let index = ((sorted_durations.len() as f64 - 1.0) * percentile) as usize;
        sorted_durations[index]
    }
}

#[derive(Debug, Serialize)]
pub struct PerformanceReport {
    pub operations: Vec<OperationStats>,
}

#[derive(Debug, Serialize)]
pub struct OperationStats {
    pub name: String,
    pub count: usize,
    pub avg_duration: Duration,
    pub min_duration: Duration,
    pub max_duration: Duration,
    pub p95_duration: Duration,
    pub p99_duration: Duration,
}

// 使用示例
#[async_trait]
impl SessionManager {
    pub async fn create_session_with_profiling(&self, config: SessionConfig) -> Result<SessionId, SessionError> {
        let _measurement = self.performance_analyzer
            .start_measurement("create_session")
            .await;

        // 实际的会话创建逻辑
        self.create_session_impl(config).await
    }
}
```

## 19. 开发指南

### 19.1 开发环境搭建

#### 19.1.1 本地开发环境

```bash
#!/bin/bash
# setup_dev_env.sh - 开发环境搭建脚本

echo "设置KVM隧道转发节点开发环境..."

# 安装Rust工具链
echo "安装Rust工具链..."
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
source ~/.cargo/env

# 安装必要的Rust组件
rustup component add rustfmt clippy
rustup target add x86_64-unknown-linux-musl

# 安装开发工具
echo "安装开发工具..."
cargo install cargo-watch cargo-audit cargo-tarpaulin cargo-criterion

# 安装系统依赖
echo "安装系统依赖..."
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    sudo apt-get update
    sudo apt-get install -y \
        build-essential \
        pkg-config \
        libssl-dev \
        libclang-dev \
        cmake \
        protobuf-compiler
elif [[ "$OSTYPE" == "darwin"* ]]; then
    brew install openssl cmake protobuf
fi

# 安装Docker和Docker Compose
echo "安装Docker..."
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    sudo usermod -aG docker $USER
fi

# 安装开发用的基础设施
echo "启动开发基础设施..."
cat > docker-compose.dev.yml << EOF
version: '3.8'
services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  etcd:
    image: quay.io/coreos/etcd:v3.5.0
    ports:
      - "2379:2379"
    environment:
      - ETCD_ADVERTISE_CLIENT_URLS=http://localhost:2379
      - ETCD_LISTEN_CLIENT_URLS=http://0.0.0.0:2379
      - ETCD_LISTEN_PEER_URLS=http://0.0.0.0:2380
      - ETCD_INITIAL_ADVERTISE_PEER_URLS=http://localhost:2380
      - ETCD_INITIAL_CLUSTER=etcd0=http://localhost:2380
      - ETCD_NAME=etcd0
    volumes:
      - etcd_data:/etcd-data

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./dev/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana

volumes:
  redis_data:
  etcd_data:
  prometheus_data:
  grafana_data:
EOF

docker-compose -f docker-compose.dev.yml up -d

echo "开发环境搭建完成！"
echo "Redis: localhost:6379"
echo "etcd: localhost:2379"
echo "Prometheus: http://localhost:9090"
echo "Grafana: http://localhost:3000 (admin/admin)"
```

#### 19.1.2 IDE配置

**VS Code配置** (`.vscode/settings.json`):
```json
{
    "rust-analyzer.cargo.features": ["dev"],
    "rust-analyzer.checkOnSave.command": "clippy",
    "rust-analyzer.checkOnSave.extraArgs": ["--", "-W", "clippy::all"],
    "files.watcherExclude": {
        "**/target/**": true
    },
    "editor.formatOnSave": true,
    "editor.rulers": [100],
    "[rust]": {
        "editor.defaultFormatter": "rust-lang.rust-analyzer",
        "editor.formatOnSave": true
    }
}
```

**VS Code任务配置** (`.vscode/tasks.json`):
```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "cargo build",
            "type": "cargo",
            "command": "build",
            "group": "build",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "cargo test",
            "type": "cargo",
            "command": "test",
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "cargo watch",
            "type": "shell",
            "command": "cargo",
            "args": ["watch", "-x", "check", "-x", "test"],
            "group": "build",
            "isBackground": true
        }
    ]
}
```

### 19.2 代码规范

#### 19.2.1 Rust编码规范

```rust
// 文件头注释模板
//! KVM隧道转发节点 - 会话管理模块
//!
//! 本模块负责管理KVM设备的会话生命周期，包括会话创建、维护和销毁。
//!
//! # 示例
//!
//! ```rust
//! use kvm_tunnel::SessionManager;
//!
//! let manager = SessionManager::new();
//! let session_id = manager.create_session(config).await?;
//! ```

use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{info, warn, error, instrument};

/// 会话管理器
///
/// 负责管理所有活跃的KVM会话，提供会话的创建、查询、更新和销毁功能。
///
/// # 线程安全
///
/// 本结构体是线程安全的，可以在多个异步任务间共享。
#[derive(Debug, Clone)]
pub struct SessionManager {
    /// 活跃会话存储
    sessions: Arc<RwLock<HashMap<SessionId, SessionInfo>>>,
    /// 配置信息
    config: SessionManagerConfig,
}

impl SessionManager {
    /// 创建新的会话管理器
    ///
    /// # 参数
    ///
    /// * `config` - 会话管理器配置
    ///
    /// # 示例
    ///
    /// ```rust
    /// let config = SessionManagerConfig::default();
    /// let manager = SessionManager::new(config);
    /// ```
    pub fn new(config: SessionManagerConfig) -> Self {
        Self {
            sessions: Arc::new(RwLock::new(HashMap::new())),
            config,
        }
    }

    /// 创建新会话
    ///
    /// # 参数
    ///
    /// * `config` - 会话配置
    ///
    /// # 返回值
    ///
    /// 成功时返回会话ID，失败时返回错误信息
    ///
    /// # 错误
    ///
    /// * `SessionError::InvalidConfig` - 配置无效
    /// * `SessionError::ResourceExhausted` - 资源不足
    #[instrument(skip(self), fields(session_type = %config.session_type))]
    pub async fn create_session(&self, config: SessionConfig) -> Result<SessionId, SessionError> {
        // 验证配置
        self.validate_config(&config)?;

        // 检查资源限制
        if self.get_session_count().await >= self.config.max_sessions {
            return Err(SessionError::ResourceExhausted);
        }

        // 创建会话
        let session_id = SessionId::new();
        let session_info = SessionInfo::new(session_id.clone(), config);

        // 存储会话
        let mut sessions = self.sessions.write().await;
        sessions.insert(session_id.clone(), session_info);

        info!(session_id = %session_id, "Session created successfully");
        Ok(session_id)
    }

    /// 验证会话配置
    fn validate_config(&self, config: &SessionConfig) -> Result<(), SessionError> {
        if config.device_id.is_empty() {
            return Err(SessionError::InvalidConfig("Device ID cannot be empty".to_string()));
        }

        // 更多验证逻辑...
        Ok(())
    }

    /// 获取当前会话数量
    async fn get_session_count(&self) -> usize {
        self.sessions.read().await.len()
    }
}

// 错误处理规范
#[derive(Debug, thiserror::Error)]
pub enum SessionError {
    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),

    #[error("Resource exhausted")]
    ResourceExhausted,

    #[error("Session not found: {0}")]
    SessionNotFound(SessionId),

    #[error("Internal error: {0}")]
    Internal(#[from] Box<dyn std::error::Error + Send + Sync>),
}

// 类型别名规范
pub type SessionId = String;
pub type DeviceId = String;
pub type Result<T> = std::result::Result<T, SessionError>;
```

#### 19.2.2 代码格式化配置

**rustfmt.toml**:
```toml
# 基本格式化选项
max_width = 100
hard_tabs = false
tab_spaces = 4

# 导入格式化
imports_granularity = "Crate"
reorder_imports = true
group_imports = "StdExternalCrate"

# 函数格式化
fn_args_layout = "Tall"
brace_style = "SameLineWhere"

# 注释格式化
comment_width = 80
wrap_comments = true
normalize_comments = true

# 字符串格式化
format_strings = true

# 宏格式化
format_macro_matchers = true
format_macro_bodies = true

# 控制流格式化
control_brace_style = "AlwaysSameLine"
```

**clippy.toml**:
```toml
# Clippy配置
avoid-breaking-exported-api = false
msrv = "1.70.0"

# 允许的lint
allow = [
    "clippy::module_name_repetitions",
    "clippy::must_use_candidate",
]

# 禁止的lint
deny = [
    "clippy::unwrap_used",
    "clippy::expect_used",
    "clippy::panic",
    "clippy::unimplemented",
    "clippy::todo",
]
```

### 19.3 测试指南

#### 19.3.1 测试结构

```
tests/
├── unit/                   # 单元测试
│   ├── session_manager.rs
│   ├── transport_manager.rs
│   └── connection_manager.rs
├── integration/            # 集成测试
│   ├── multi_node.rs
│   └── end_to_end.rs
├── load/                   # 负载测试
│   ├── concurrent_sessions.rs
│   └── high_throughput.rs
├── security/               # 安全测试
│   ├── authentication.rs
│   └── authorization.rs
└── fixtures/               # 测试数据
    ├── certificates/
    └── configs/
```

#### 19.3.2 测试工具函数

```rust
// tests/common/mod.rs
use kvm_tunnel::*;
use std::sync::Once;
use tracing_subscriber;

static INIT: Once = Once::new();

/// 初始化测试环境
pub fn init_test_env() {
    INIT.call_once(|| {
        tracing_subscriber::fmt()
            .with_env_filter("debug")
            .with_test_writer()
            .init();
    });
}

/// 创建测试用的会话配置
pub fn create_test_session_config() -> SessionConfig {
    SessionConfig {
        session_type: SessionType::Transmitter,
        device_id: "test_device_001".to_string(),
        transports: vec![
            TransportConfig {
                transport_type: TransportType::WebRTC,
                bind_address: "127.0.0.1:0".parse().unwrap(),
                ..Default::default()
            }
        ],
    }
}

/// 创建测试用的转发节点
pub async fn create_test_forwarder_node() -> ForwarderNode {
    let config = ForwarderConfig {
        node_id: format!("test_node_{}", uuid::Uuid::new_v4()),
        listen_ip: "127.0.0.1".to_string(),
        listen_port: 0, // 使用随机端口
        ..Default::default()
    };

    ForwarderNode::new(config).await.expect("Failed to create test node")
}

/// 等待条件满足或超时
pub async fn wait_for_condition<F, Fut>(
    condition: F,
    timeout: Duration,
    check_interval: Duration,
) -> bool
where
    F: Fn() -> Fut,
    Fut: Future<Output = bool>,
{
    let start = Instant::now();

    while start.elapsed() < timeout {
        if condition().await {
            return true;
        }
        tokio::time::sleep(check_interval).await;
    }

    false
}

/// 断言最终条件
#[macro_export]
macro_rules! assert_eventually {
    ($condition:expr, $timeout:expr) => {
        assert!(
            wait_for_condition(|| async { $condition }, $timeout, Duration::from_millis(100)).await,
            "Condition was not met within timeout"
        );
    };
}
```

## 20. 故障排查指南

### 11.1 项目结构

```
kvm_tunnel/
├── Cargo.toml                    # 项目配置
├── src/
│   ├── main.rs                   # 程序入口
│   ├── lib.rs                    # 库入口
│   ├── config/                   # 配置管理
│   │   ├── mod.rs
│   │   ├── node_config.rs
│   │   └── transport_config.rs
│   ├── core/                     # 核心模块
│   │   ├── mod.rs
│   │   ├── node_manager.rs
│   │   ├── session_manager.rs
│   │   ├── transport_manager.rs
│   │   └── connection_manager.rs
│   ├── transport/                # 传输层
│   │   ├── mod.rs
│   │   ├── mediasoup/
│   │   │   ├── mod.rs
│   │   │   ├── engine.rs
│   │   │   └── worker.rs
│   │   ├── port_forward/
│   │   │   ├── mod.rs
│   │   │   ├── tcp_tunnel.rs
│   │   │   └── udp_tunnel.rs
│   │   └── multicast/
│   │       ├── mod.rs
│   │       └── controller.rs
│   ├── network/                  # 网络层
│   │   ├── mod.rs
│   │   ├── api_server.rs
│   │   ├── websocket_server.rs
│   │   └── protocol.rs
│   ├── management/               # 管理层
│   │   ├── mod.rs
│   │   ├── config_manager.rs
│   │   ├── log_manager.rs
│   │   ├── monitor_manager.rs
│   │   └── security_manager.rs
│   ├── storage/                  # 存储层
│   │   ├── mod.rs
│   │   ├── memory_store.rs
│   │   └── cache_manager.rs
│   ├── types/                    # 类型定义
│   │   ├── mod.rs
│   │   ├── node.rs
│   │   ├── session.rs
│   │   ├── transport.rs
│   │   └── error.rs
│   └── utils/                    # 工具函数
│       ├── mod.rs
│       ├── crypto.rs
│       └── network.rs
├── tests/                        # 测试文件
│   ├── integration/
│   └── unit/
├── examples/                     # 示例代码
├── docs/                         # 文档
├── scripts/                      # 构建脚本
└── deployment/                   # 部署配置
    └── docker/
```

### 11.2 核心依赖

```toml
[dependencies]
# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# Web框架
axum = "0.7"
tower = "0.4"
tower-http = { version = "0.5", features = ["cors"] }

# 序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# 网络
hyper = "1.0"
tungstenite = "0.20"
tokio-tungstenite = "0.20"

# 并发数据结构
dashmap = "5.0"
arc-swap = "1.0"

# 配置管理
config = "0.13"
clap = { version = "4.0", features = ["derive"] }

# 加密
ring = "0.16"
jsonwebtoken = "8.0"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.0", features = ["v4", "serde"] }

# MediaSoup (假设有Rust绑定)
mediasoup = "0.12"

# etcd客户端
etcd-rs = "1.0"
```

### 11.3 开发环境设置

#### 11.3.1 本地开发环境
```bash
# 安装Rust工具链
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# 安装必要工具
cargo install cargo-watch
cargo install cargo-nextest
cargo install cargo-audit

# 克隆项目
git clone <repository-url>
cd kvm_tunnel

# 安装依赖
cargo build

# 运行测试
cargo test

# 启动开发服务器
cargo run -- --config config/dev.toml
```

#### 11.3.2 调试配置
```json
// .vscode/launch.json
{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug forwarder node",
            "cargo": {
                "args": [
                    "build",
                    "--bin=kvm_tunnel"
                ],
                "filter": {
                    "name": "kvm_tunnel",
                    "kind": "bin"
                }
            },
            "args": ["--config", "config/dev.toml"],
            "cwd": "${workspaceFolder}",
            "env": {
                "RUST_LOG": "debug",
                "RUST_BACKTRACE": "1"
            }
        }
    ]
}
```

### 11.4 测试策略

#### 11.4.1 单元测试
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;
    use tokio::time::timeout;

    #[tokio::test]
    async fn test_session_creation() {
        let manager = SessionManager::new();
        let config = TransmitterConfig {
            name: "test".to_string(),
            transports: vec![],
        };
        
        let session_id = manager.create_transmitter_session(config).await.unwrap();
        assert!(!session_id.is_empty());
        
        let session_info = manager.get_session_info(&session_id).await.unwrap();
        assert_eq!(session_info.session_type, SessionType::Transmitter);
    }

    #[tokio::test]
    async fn test_cross_node_communication() {
        let node1 = create_test_node("node1").await;
        let node2 = create_test_node("node2").await;
        
        let result = timeout(
            Duration::from_secs(5),
            node1.request_cross_node_access("node2", test_config())
        ).await;
        
        assert!(result.is_ok());
    }
}
```

#### 11.4.2 集成测试
```rust
// tests/integration/node_communication.rs
use kvm_tunnel::*;
use std::time::Duration;
use tokio::time::sleep;

#[tokio::test]
async fn test_full_forwarding_flow() {
    // 启动模拟的管理服务器
    let ms = start_mock_management_server().await;
    
    // 启动两个转发节点
    let node1 = start_forwarder_node("node1", ms.url()).await;
    let node2 = start_forwarder_node("node2", ms.url()).await;
    
    // 创建TX设备会话
    let tx_session = node2.create_transmitter_session(create_tx_config()).await.unwrap();
    
    // 创建RX设备会话
    let rx_session = node1.create_receiver_session(create_rx_config(tx_session)).await.unwrap();
    
    // 验证数据流通
    sleep(Duration::from_millis(100)).await;
    
    let tx_stats = node2.get_session_stats(&tx_session).await.unwrap();
    let rx_stats = node1.get_session_stats(&rx_session).await.unwrap();
    
    assert!(tx_stats.bytes_sent > 0);
    assert!(rx_stats.bytes_received > 0);
    
    // 清理
    node1.destroy_session(&rx_session).await.unwrap();
    node2.destroy_session(&tx_session).await.unwrap();
}
```

### 11.5 性能基准测试

#### 11.5.1 基准测试框架
```rust
// benches/forwarding_performance.rs
use criterion::{black_box, criterion_group, criterion_main, Criterion};
use kvm_tunnel::*;

fn benchmark_session_creation(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();
    let manager = rt.block_on(async { SessionManager::new() });
    
    c.bench_function("session_creation", |b| {
        b.iter(|| {
            rt.block_on(async {
                let config = create_test_config();
                let session_id = manager.create_transmitter_session(config).await.unwrap();
                manager.destroy_session(&session_id).await.unwrap();
            })
        })
    });
}

fn benchmark_data_forwarding(c: &mut Criterion) {
    let rt = tokio::runtime::Runtime::new().unwrap();
    let setup = rt.block_on(async {
        setup_forwarding_test().await
    });
    
    c.bench_function("data_forwarding_1kb", |b| {
        b.iter(|| {
            rt.block_on(async {
                let data = vec![0u8; 1024];
                setup.forward_data(black_box(data)).await.unwrap();
            })
        })
    });
}

criterion_group!(benches, benchmark_session_creation, benchmark_data_forwarding);
criterion_main!(benches);
```

## 12. 故障排查指南

### 12.1 常见问题及解决方案

| 问题 | 症状 | 排查步骤 | 解决方案 |
|------|------|----------|----------|
| 连接超时 | 客户端无法连接到转发节点 | 1. 检查网络连通性<br/>2. 验证防火墙配置<br/>3. 查看节点状态 | 1. 修复网络配置<br/>2. 开放必要端口<br/>3. 重启故障节点 |
| 音视频卡顿 | 媒体流不稳定 | 1. 检查带宽使用情况<br/>2. 查看CPU/内存使用率<br/>3. 分析网络延迟 | 1. 增加带宽<br/>2. 扩容节点<br/>3. 优化路由 |
| 会话创建失败 | API返回错误 | 1. 查看详细错误日志<br/>2. 检查资源使用情况<br/>3. 验证配置有效性 | 1. 修复配置错误<br/>2. 释放资源<br/>3. 重启服务 |

### 12.2 调试命令
```bash
# 查看节点状态
curl -s http://forwarder-node:8080/api/v1/status | jq

# 查看活跃会话
curl -s http://forwarder-node:8080/api/v1/sessions | jq

# 检查健康状态
curl -s http://forwarder-node:8080/health

# 查看日志
docker logs -f forwarder-node-container

# 查看资源使用情况
docker stats forwarder-node-container

# 网络连通性测试
ping target-ip
telnet target-ip target-port
```

---

*本文档将随着项目开发持续更新和完善。*
